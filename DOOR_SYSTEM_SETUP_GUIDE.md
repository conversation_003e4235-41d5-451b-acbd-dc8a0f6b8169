# 🚪 Multi-Door System Setup Guide

## ✅ **Quick Verification Checklist**

### 1. **Input Actions File Check**
- Open `VehicleInputActions.inputactions` in Unity
- Verify you see these actions under the Vehicle action map:
  - DoorToggle (O key)
  - Door1Toggle (1 key)
  - Door2Toggle (2 key)
  - Door3Toggle (3 key)
  - Door4Toggle (4 key)
  - Door5Toggle (5 key)
  - <PERSON>6Toggle (6 key)

### 2. **Code Compilation Check**
- Ensure no compilation errors in Console
- All scripts should compile successfully

### 3. **Door Setup Check**
- Each door GameObject should have:
  - `JrsDoorMechanic` component
  - Unique `doorID` (Door1, Door2, Door3, Door4, etc.)
  - Appropriate `toggleKey` for fallback

## 🔧 **Step-by-Step Setup**

### **Step 1: Configure Your Doors**

For each door in your vehicle:

1. **Add JrsDoorMechanic Component**
   ```
   Select Door GameObject → Add Component → JrsDoorMechanic
   ```

2. **Set Door ID**
   ```
   Front Left Door:  doorID = "Door1"
   Front Right Door: doorID = "Door2"
   Rear Left Door:   doorID = "Door3"
   Rear Right Door:  doorID = "Door4"
   Additional doors: "Door5", "Door6", etc.
   ```

3. **Set Fallback Keys** (for old input system)
   ```
   Door1: toggleKey = Alpha1
   Door2: toggleKey = Alpha2
   Door3: toggleKey = Alpha3
   Door4: toggleKey = Alpha4
   ```

### **Step 2: Configure Input Controller**

1. **Find JrsNewInputController in your scene**
2. **Assign Mobile Buttons** (optional):
   ```
   door1Button → UI Button for Door 1
   door2Button → UI Button for Door 2
   door3Button → UI Button for Door 3
   door4Button → UI Button for Door 4
   ```

### **Step 3: Test the System**

1. **Add the Tester Script**
   ```
   Create Empty GameObject → Add Component → JrsDoorSystemTester
   ```

2. **Test Controls**
   ```
   Keyboard Keys:
   - 1: Toggle Door1
   - 2: Toggle Door2
   - 3: Toggle Door3
   - 4: Toggle Door4
   - 5: Toggle Door5
   - 6: Toggle Door6
   - O: Generic door toggle
   - T: Test all doors at once
   ```

## 🎮 **Control Mapping**

| Key | Action | Door |
|-----|--------|------|
| 1 | Door1Toggle | Front Left |
| 2 | Door2Toggle | Front Right |
| 3 | Door3Toggle | Rear Left |
| 4 | Door4Toggle | Rear Right |
| 5 | Door5Toggle | Additional Door 1 |
| 6 | Door6Toggle | Additional Door 2 |
| O | DoorToggle | Generic (first available) |

## 🚗 **Vehicle Type Examples**

### **4-Door Sedan**
```
Door_FL → doorID = "Door1" (Key: 1)
Door_FR → doorID = "Door2" (Key: 2)
Door_RL → doorID = "Door3" (Key: 3)
Door_RR → doorID = "Door4" (Key: 4)
```

### **2-Door Sports Car**
```
Door_L → doorID = "Door1" (Key: 1)
Door_R → doorID = "Door2" (Key: 2)
```

### **6-Door Limousine**
```
Door_FL → doorID = "Door1" (Key: 1)
Door_FR → doorID = "Door2" (Key: 2)
Door_ML → doorID = "Door3" (Key: 3)
Door_MR → doorID = "Door4" (Key: 4)
Door_RL → doorID = "Door5" (Key: 5)
Door_RR → doorID = "Door6" (Key: 6)
```

## 🔍 **Troubleshooting**

### **Issue: Input Actions Error**
- **Solution**: Regenerate the input actions
  1. Select `VehicleInputActions.inputactions`
  2. Click "Generate C# Class"
  3. Ensure the generated file matches the .inputactions file

### **Issue: Doors Not Responding**
- **Check**: Vehicle is selected in JrsVehicleSelector
- **Check**: useNewInputSystem is enabled on JrsVehicleController
- **Check**: doorID matches exactly (case-sensitive)

### **Issue: Mobile Buttons Not Working**
- **Check**: enableMobileControls is true
- **Check**: Mobile buttons are assigned in JrsNewInputController
- **Check**: Buttons have JrsCustomButton component

## 📱 **Mobile Setup**

1. **Create UI Buttons** for each door
2. **Add JrsCustomButton** component to each button
3. **Assign buttons** in JrsNewInputController:
   ```
   door1Button → Door 1 UI Button
   door2Button → Door 2 UI Button
   etc.
   ```

## ✨ **Features**

- ✅ Individual door control (1-6 keys)
- ✅ Mobile touch support
- ✅ Backward compatibility (O key)
- ✅ Scalable (1-6+ doors)
- ✅ Input system agnostic
- ✅ Vehicle-specific activation
- ✅ Audio feedback support
- ✅ Animation support

## 🎯 **Testing Commands**

Use the `JrsDoorSystemTester` script to verify:

```
T - Test all doors at once
1-6 - Test individual doors
O - Test generic door toggle
```

The tester will show debug information in the Console and on-screen GUI.
