using UnityEngine;

public class JrsCameraManager : MonoBehaviour
{
    public Camera[] cameras;
    // toggleKeys removed - camera switching now handled by New Input System

    private int activeCameraIndex = 0;

    private void Start()
    {
        // Disable all cameras except the first one
        for (int i = 1; i < cameras.Length; i++)
        {
            cameras[i].gameObject.SetActive(false);
        }

        // Ensure proper AudioListener setup
        SetupAudioListeners();
    }

    private void Update()
    {
        // Individual camera toggle keys are now deprecated
        // Camera switching is handled by the input controller via SwitchToNextCamera()
        // This Update method is kept empty for backward compatibility
    }

    public void SwitchToNextCamera()
    {
        int nextIndex = (activeCameraIndex + 1) % cameras.Length;
        SwitchToCamera(nextIndex);
    }

    private void SwitchToCamera(int index)
    {
        // Disable the currently active camera
        cameras[activeCameraIndex].gameObject.SetActive(false);

        // Enable the new camera
        cameras[index].gameObject.SetActive(true);

        // Update the active camera index
        activeCameraIndex = index;

        // Ensure proper AudioListener management
        ManageAudioListeners();
    }

    public void SetActiveCamera(Transform vehicleTransform)
    {
        if (vehicleTransform == null)
        {
            Debug.LogError("Vehicle transform is null");
            return;
        }

        foreach (Camera cam in cameras)
        {
            if (cam == null) continue;

            JrsDashCamera dashCam = cam.GetComponent<JrsDashCamera>();
            if (dashCam != null)
            {
                Transform player = vehicleTransform.Find("player");
                if (player != null) dashCam.player = player;
            }

            JrsOrbitCamera orbitCam = cam.GetComponent<JrsOrbitCamera>();
            if (orbitCam != null)
            {
                Transform target = vehicleTransform.Find("target");
                if (target != null) orbitCam.target = target;
            }

            JrsFollowCamera followCam = cam.GetComponent<JrsFollowCamera>();
            if (followCam != null)
            {
                Transform target = vehicleTransform.Find("target");
                if (target != null) followCam.target = target;
            }
        }
    }

    public void RefreshCameraTargets(Transform vehicleTransform)
    {
        SetActiveCamera(vehicleTransform);
    }

    /// <summary>
    /// Sets up AudioListeners on all cameras, ensuring only one is active at a time
    /// </summary>
    private void SetupAudioListeners()
    {
        for (int i = 0; i < cameras.Length; i++)
        {
            if (cameras[i] == null) continue;

            // Ensure each camera has an AudioListener component
            AudioListener listener = cameras[i].GetComponent<AudioListener>();
            if (listener == null)
            {
                listener = cameras[i].gameObject.AddComponent<AudioListener>();
            }

            // Only enable the AudioListener on the active camera
            listener.enabled = (i == activeCameraIndex);
        }

        Debug.Log($"AudioListener setup complete. Active camera: {activeCameraIndex}");
    }

    /// <summary>
    /// Public method to refresh AudioListener setup (called by vehicle selector)
    /// </summary>
    public void RefreshAudioListeners()
    {
        SetupAudioListeners();
    }

    /// <summary>
    /// Manages AudioListeners when switching cameras to ensure proper 3D audio positioning
    /// </summary>
    private void ManageAudioListeners()
    {
        for (int i = 0; i < cameras.Length; i++)
        {
            if (cameras[i] == null) continue;

            AudioListener listener = cameras[i].GetComponent<AudioListener>();
            if (listener != null)
            {
                // Only enable the AudioListener on the currently active camera
                listener.enabled = (i == activeCameraIndex);
            }
        }

        Debug.Log($"AudioListener switched to camera {activeCameraIndex}: {cameras[activeCameraIndex].name}");
    }
}
