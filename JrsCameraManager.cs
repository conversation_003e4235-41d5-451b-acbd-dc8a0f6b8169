using UnityEngine;

public class JrsCameraManager : MonoBehaviour
{
    public Camera[] cameras;
    [System.Obsolete("Individual toggle keys are deprecated. Use SwitchToNextCamera() method instead.")]
    public KeyCode[] toggleKeys; // Kept for backward compatibility but not used

    private int activeCameraIndex = 0;

    private void Start()
    {
        // Disable all cameras except the first one
        for (int i = 1; i < cameras.Length; i++)
        {
            cameras[i].gameObject.SetActive(false);
        }
    }

    private void Update()
    {
        // Individual camera toggle keys are now deprecated
        // Camera switching is handled by the input controller via SwitchToNextCamera()
        // This Update method is kept empty for backward compatibility
    }

    public void SwitchToNextCamera()
    {
        int nextIndex = (activeCameraIndex + 1) % cameras.Length;
        SwitchToCamera(nextIndex);
    }

    private void SwitchToCamera(int index)
    {
        // Disable the currently active camera
        cameras[activeCameraIndex].gameObject.SetActive(false);

        // Enable the new camera
        cameras[index].gameObject.SetActive(true);

        // Update the active camera index
        activeCameraIndex = index;
    }

    public void SetActiveCamera(Transform vehicleTransform)
    {
        if (vehicleTransform == null)
        {
            Debug.LogError("Vehicle transform is null");
            return;
        }

        foreach (Camera cam in cameras)
        {
            if (cam == null) continue;

            JrsDashCamera dashCam = cam.GetComponent<JrsDashCamera>();
            if (dashCam != null)
            {
                Transform player = vehicleTransform.Find("player");
                if (player != null) dashCam.player = player;
            }

            JrsOrbitCamera orbitCam = cam.GetComponent<JrsOrbitCamera>();
            if (orbitCam != null)
            {
                Transform target = vehicleTransform.Find("target");
                if (target != null) orbitCam.target = target;
            }

            JrsFollowCamera followCam = cam.GetComponent<JrsFollowCamera>();
            if (followCam != null)
            {
                Transform target = vehicleTransform.Find("target");
                if (target != null) followCam.target = target;
            }
        }
    }

    public void RefreshCameraTargets(Transform vehicleTransform)
    {
        SetActiveCamera(vehicleTransform);
    }
}
