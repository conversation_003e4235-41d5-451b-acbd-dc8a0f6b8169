using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;

public class JrsVehicleController : MonoBehaviour
{
    [Header("Vehicle Settings")]
    public float motorForce = 50f;
    private float maxSteerAngle = 30f;
    public float maxSteerAngleFront = 30f;
    public float maxSteerAngleRear = 30f;
    public WheelCollider[] wheels;
    public Transform[] wheelTransforms;
    public GameObject centerOfMassObject;
    public WheelCollider[] wheelCollidersBrake;
    public float brakeForce = 500f;

    [Header("Drive Settings")]
    public float[] gearRatios;
    public float shiftThreshold = 5000f;
    public bool enableAllWheelDrive = false;
    private int currentGear = 1;
    private float stopSpeedThreshold = 1f;

    [Header("Effects")]
    public ParticleSystem frontLeftDustParticleSystem;
    public ParticleSystem frontRightDustParticleSystem;
    public ParticleSystem rearLeftDustParticleSystem;
    public ParticleSystem rearRightDustParticleSystem;
    public AudioSource engineAudioSource;
    public AudioSource engineStartAudioSource;

    private Quaternion prevRotation;
    private JrsInputController mobileInputController;
    private JrsNewInputController newInputController;
    public bool canControl = true;

    [Header("Input System Selection")]
    [Tooltip("Toggle between old and new input system")]
    public bool useNewInputSystem = false; // Toggle between old and new input system
    private AudioClip engineSound;
    private float targetPitch;
    private bool hasStartedMoving = false;
    private Rigidbody rb;
    private JrsVehicleLightControl lightControl;
    private float idleTimer = 0f;
    private bool isEngineSoundPlaying = false;

    void Start()
    {
        rb = GetComponent<Rigidbody>();
        prevRotation = wheelTransforms[0].rotation;
        lightControl = GetComponent<JrsVehicleLightControl>();
        mobileInputController = FindObjectOfType<JrsInputController>();
        newInputController = FindObjectOfType<JrsNewInputController>();

        engineSound = Resources.Load<AudioClip>("EngineSound");
        targetPitch = engineAudioSource.pitch;

        StartCoroutine(DelayedEngineSound());
    }

    public void SetActive(bool active)
    {
        if (lightControl != null)
        {
            lightControl.enabled = active;
        }

        JrsDoorMechanic doorMechanic = GetComponent<JrsDoorMechanic>();
        if (doorMechanic != null)
        {
            doorMechanic.enabled = active;
        }
    }

    IEnumerator DelayedEngineSound()
    {
        while (!hasStartedMoving)
        {
            yield return null;
        }
        yield return new WaitForSeconds(0.8f);
        engineAudioSource.Play();
    }

    bool IsWheelSlipping(WheelCollider wheel)
    {
        WheelHit hit;
        return wheel.GetGroundHit(out hit) && hit.sidewaysSlip > 0.1f;
    }

    bool IsWheelDrifting(WheelCollider wheel)
    {
        WheelHit hit;
        return wheel.GetGroundHit(out hit) && hit.forwardSlip > 0.1f;
    }

    bool IsWheelBraking(WheelCollider wheel)
    {
        return wheel.isGrounded && Mathf.Abs(wheel.rpm) < 1f && wheel.brakeTorque > 0f;
    }

    void SetDustParticleSystemState(ParticleSystem dustParticleSystem, bool shouldPlay)
    {
        if (dustParticleSystem == null) return;
        
        var emission = dustParticleSystem.emission;
        var main = dustParticleSystem.main;
        var velocityOverLifetime = dustParticleSystem.velocityOverLifetime;
        
        if (shouldPlay)
        {
            // Calculate intensity based on speed and slip
            float speedFactor = Mathf.Clamp01(rb.velocity.magnitude / 20f);
            float slipFactor = 0f;
            
            foreach (WheelCollider wheel in wheels)
            {
                WheelHit hit;
                if (wheel.GetGroundHit(out hit))
                {
                    slipFactor += Mathf.Abs(hit.sidewaysSlip) + Mathf.Abs(hit.forwardSlip);
                }
            }
            slipFactor = Mathf.Clamp01(slipFactor / wheels.Length);
            
            // Adjust emission rate based on speed
            emission.rateOverTime = Mathf.Lerp(10f, 100f, speedFactor);
            
            // Adjust particle size based on slip
            main.startSize = Mathf.Lerp(0.1f, 0.5f, slipFactor);
            
            // Add velocity over lifetime for more dynamic movement
            velocityOverLifetime.enabled = true;
            velocityOverLifetime.space = ParticleSystemSimulationSpace.World;
            velocityOverLifetime.x = Mathf.Lerp(-2f, 2f, slipFactor);
            velocityOverLifetime.y = Mathf.Lerp(0.5f, 2f, slipFactor);
            
            if (!dustParticleSystem.isPlaying)
            {
                dustParticleSystem.Play();
            }
        }
        else
        {
            if (dustParticleSystem.isPlaying)
            {
                dustParticleSystem.Stop();
                emission.rateOverTime = 0;
            }
        }
    }

    void UpdateWheelPoses()
    {
        for (int i = 0; i < wheels.Length; i++)
        {
            bool flip = (i % 2 == 1);
            UpdateWheelPose(wheels[i], wheelTransforms[i], flip);
        }
    }

    void UpdateWheelPose(WheelCollider collider, Transform transform, bool flip = false)
    {
        Vector3 pos = transform.position;
        Quaternion quat = transform.rotation;

        collider.GetWorldPose(out pos, out quat);

        if (flip)
        {
            quat *= Quaternion.Euler(0, 180, 0);
        }

        transform.position = pos;
        transform.rotation = quat;
    }

    void Update()
    {
        if (centerOfMassObject)
        {
            rb.centerOfMass = transform.InverseTransformPoint(centerOfMassObject.transform.position);
        }

        float v = GetVerticalInput() * motorForce;
        float h = GetHorizontalInput() * maxSteerAngle;

        if (canControl)
        {
            // Apply motor torque to the wheels
            foreach (WheelCollider wheel in wheels)
            {
                if (enableAllWheelDrive)
                {
                    wheel.motorTorque = v;
                }
                else
                {
                    wheel.motorTorque = wheel == wheels[0] || wheel == wheels[1] ? v : 0f;
                }
            }

            // Apply steering angle to the front wheels
            wheels[0].steerAngle = h;
            wheels[1].steerAngle = h;

            // Only apply rear wheel steering for player-controlled vehicles
            wheels[2].steerAngle = h * maxSteerAngleRear;
            wheels[3].steerAngle = h * maxSteerAngleRear;

            // Handle brake input and brake lights
            bool isBraking = GetBrakeInput();
            if (isBraking)
            {
                foreach (WheelCollider wheelCollider in wheelCollidersBrake)
                {
                    wheelCollider.brakeTorque = brakeForce;
                }
            }
            else
            {
                foreach (WheelCollider wheelCollider in wheelCollidersBrake)
                {
                    wheelCollider.brakeTorque = 0;
                }
            }

            // Update brake and reverse lights
            if (lightControl != null)
            {
                lightControl.SetBrakeLights(isBraking);
                lightControl.SetReverseLights(v < -0.1f);
            }
        }

        // Update wheel poses
        UpdateWheelPoses();
    }

    void FixedUpdate()
    {
        float v = GetVerticalInput() * motorForce;
        float h = GetHorizontalInput() * maxSteerAngle;

        // Calculate the current wheel speed in km/h
        float currentSpeedKmph = wheels[0].radius * Mathf.PI * wheels[0].rpm * 60f / 1000f;

        // Calculate the current engine RPM based on the wheel speed and gear ratio
        float currentRPM = wheels[0].rpm * gearRatios[Mathf.Clamp(currentGear - 1, 0, gearRatios.Length - 1)];

        if (canControl)
        {
            // Handle gear shifting
            if (currentRPM > shiftThreshold && currentGear < gearRatios.Length)
            {
                currentGear++;
            }
            else if (currentSpeedKmph < stopSpeedThreshold && currentGear > 1)
            {
                currentGear--;
            }

            // Adjust the motor torque based on the current gear ratio
            float adjustedTorque = v * gearRatios[Mathf.Clamp(currentGear - 1, 0, gearRatios.Length - 1)];

            // Apply motor torque to the wheels
            foreach (WheelCollider wheel in wheels)
            {
                if (enableAllWheelDrive || wheelTransforms[0] == wheelTransforms[1])
                {
                    wheel.motorTorque = adjustedTorque;
                }
                else
                {
                    wheel.motorTorque = wheel == wheels[0] || wheel == wheels[1] ? adjustedTorque : 0f;
                }
            }

            // Apply steering angle to the front wheels
            wheels[0].steerAngle = h;
            wheels[1].steerAngle = h;
        }

        // Check if the vehicle is moving
        bool isMoving = rb.velocity.magnitude > 0.1f;

        // Calculate the wheel's angular velocity
        Quaternion currentRotation = wheelTransforms[0].rotation;
        float angularVelocity = Quaternion.Angle(prevRotation, currentRotation) / Time.fixedDeltaTime;
        prevRotation = currentRotation;

        bool isSlipping = false;
        bool isDrifting = false;
        bool isBraking = false;

        foreach (WheelCollider wheel in wheels)
        {
            isSlipping |= IsWheelSlipping(wheel);
            isDrifting |= IsWheelDrifting(wheel);
            isBraking |= IsWheelBraking(wheel);
        }

        bool shouldPlayDustParticles = isSlipping || isDrifting || isBraking || isMoving;

        SetDustParticleSystemState(frontLeftDustParticleSystem, shouldPlayDustParticles);
        SetDustParticleSystemState(frontRightDustParticleSystem, shouldPlayDustParticles);
        SetDustParticleSystemState(rearLeftDustParticleSystem, shouldPlayDustParticles);
        SetDustParticleSystemState(rearRightDustParticleSystem, shouldPlayDustParticles);

        // Handle engine sound and reverse lights
        if (lightControl != null)
        {
            lightControl.SetReverseLights(currentSpeedKmph < -0.1f);
        }
        
        float targetPitch = currentSpeedKmph > 0.1f ? Mathf.Lerp(0.5f, 1.2f, currentSpeedKmph / 100f) : 0.5f;
        if (currentSpeedKmph < -0.1f)
        {
            targetPitch = Mathf.Lerp(0.5f, 1.2f, Mathf.Abs(currentSpeedKmph) / 100f);
        }

        engineAudioSource.pitch = Mathf.Lerp(engineAudioSource.pitch, targetPitch, Time.deltaTime * 5f);

        // Update wheel poses
        UpdateWheelPoses();

        // Ensure the engine sound is playing
        if (!isEngineSoundPlaying && hasStartedMoving)
        {
            engineAudioSource.Play();
            isEngineSoundPlaying = true;
        }
        else if (!isMoving)
        {
            // Increment the idle timer
            idleTimer += Time.fixedDeltaTime;

            // Check if the vehicle has been idle for 30 seconds
            if (idleTimer >= 30f && isEngineSoundPlaying)
            {
                // Stop the engine sound
                engineAudioSource.Stop();
                isEngineSoundPlaying = false;
                hasStartedMoving = false;
            }
        }
        else
        {
            idleTimer = 0f;
        }

        // Play the engine start sound if the vehicle just starts moving
        if (!hasStartedMoving && isMoving)
        {
            engineStartAudioSource.Play();
            hasStartedMoving = true;
            isEngineSoundPlaying = true;
            StartCoroutine(DelayedEngineSound());
        }
    }

    // Helper methods to get input from either old or new input system
    private float GetVerticalInput()
    {
        if (useNewInputSystem && newInputController != null)
        {
            return newInputController.GetVerticalInput();
        }
        else if (mobileInputController != null)
        {
            return mobileInputController.GetVerticalInput();
        }
        else
        {
            return Input.GetAxis("Vertical");
        }
    }

    private float GetHorizontalInput()
    {
        if (useNewInputSystem && newInputController != null)
        {
            return newInputController.GetHorizontalInput();
        }
        else if (mobileInputController != null)
        {
            return mobileInputController.GetHorizontalInput();
        }
        else
        {
            return Input.GetAxis("Horizontal");
        }
    }

    private bool GetBrakeInput()
    {
        if (useNewInputSystem && newInputController != null)
        {
            return newInputController.IsBraking();
        }
        else if (mobileInputController != null && mobileInputController.brakeButton != null)
        {
            return mobileInputController.brakeButton.IsButtonPressed();
        }
        else
        {
            return Input.GetKey(KeyCode.Space);
        }
    }

    // Public method to switch input systems at runtime
    public void SetInputSystem(bool useNew)
    {
        useNewInputSystem = useNew;
        Debug.Log("Switched to " + (useNew ? "New Input System" : "Old Input System"));
    }
}
