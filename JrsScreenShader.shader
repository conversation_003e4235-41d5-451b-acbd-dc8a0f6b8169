Shader "Custom/JrsScreenShader"
{
    Properties
    {
        [PerRendererData] _MainTex ("Texture", 2D) = "white" {}
        _Brightness ("Brightness", Range(0.5, 2)) = 1
        _Contrast ("Contrast", Range(0.5, 2)) = 1 
        _Saturation ("Saturation", Range(0, 1)) = 1
    }

    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 100

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            
            UNITY_INSTANCING_BUFFER_START(Props)
                UNITY_DEFINE_INSTANCED_PROP(float, _Brightness)
                UNITY_DEFINE_INSTANCED_PROP(float, _Contrast)
                UNITY_DEFINE_INSTANCED_PROP(float, _Saturation)
            UNITY_INSTANCING_BUFFER_END(Props)

            v2f vert (appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(i);
                
                float brightness = UNITY_ACCESS_INSTANCED_PROP(Props, _Brightness);
                float contrast = UNITY_ACCESS_INSTANCED_PROP(Props, _Contrast);
                float saturation = UNITY_ACCESS_INSTANCED_PROP(Props, _Saturation);

                fixed4 col = tex2D(_MainTex, i.uv);
                
                // Apply brightness
                col.rgb *= brightness;
                
                // Apply contrast
                col.rgb = ((col.rgb - 0.5) * max(contrast, 0.0001)) + 0.5;
                
                // Apply saturation
                fixed luminance = dot(col.rgb, fixed3(0.299, 0.587, 0.114));
                col.rgb = lerp(fixed3(luminance, luminance, luminance), col.rgb, saturation);
                
                return col;
            }
            ENDCG
        }
    }
}
