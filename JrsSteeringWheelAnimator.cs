using UnityEngine;

public class JrsSteeringWheelAnimator : MonoBehaviour
{
    public Transform steeringWheelTransform;
    public float maxSteerAngle = 30f;
    public float rotationSpeed = 100f;

    [Header("Input System Selection")]
    [Tooltip("Toggle between old and new input system (auto-syncs with Vehicle Controller)")]
    public bool useNewInputSystem = false; // Toggle between old and new input system

    private float targetRotation = 0f;
    private JrsInputController inputController; // Reference to the old input controller
    private JrsNewInputController newInputController; // Reference to the new input controller
    private JrsVehicleController vehicleController; // Reference to vehicle controller for fallback

    void Start()
    {
        // Find both input controllers in the scene
        inputController = FindObjectOfType<JrsInputController>();
        newInputController = FindObjectOfType<JrsNewInputController>();
        vehicleController = FindObjectOfType<JrsVehicleController>();

        // Check if we have at least one input source
        if (inputController == null && newInputController == null && vehicleController == null)
        {
            Debug.LogError("No input controller found in the scene!");
            enabled = false; // Disable this script if no input controller is found
        }

        // Auto-detect which input system to use if vehicle controller is available
        if (vehicleController != null)
        {
            useNewInputSystem = vehicleController.useNewInputSystem;
        }
    }

    private void Update()
    {
        // Auto-sync with vehicle controller's input system setting
        if (vehicleController != null)
        {
            useNewInputSystem = vehicleController.useNewInputSystem;
        }

        // Get the horizontal input from the appropriate input controller
        float steeringInput = GetHorizontalInput();
        targetRotation = steeringInput * maxSteerAngle;

        float currentRotation = steeringWheelTransform.localRotation.eulerAngles.y;
        float newRotation = Mathf.MoveTowardsAngle(currentRotation, targetRotation, rotationSpeed * Time.deltaTime);

        steeringWheelTransform.localRotation = Quaternion.Euler(0f, newRotation, 0f);
    }

    // Helper method to get horizontal input from the appropriate input system
    private float GetHorizontalInput()
    {
        if (useNewInputSystem && newInputController != null)
        {
            return newInputController.GetHorizontalInput();
        }
        else if (inputController != null)
        {
            return inputController.GetHorizontalInput();
        }
        else
        {
            // Fallback to Unity's default input system
            return Input.GetAxis("Horizontal");
        }
    }

    // Public method to manually set which input system to use
    public void SetInputSystem(bool useNew)
    {
        useNewInputSystem = useNew;
    }
}
