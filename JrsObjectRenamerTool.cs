using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

public class JrsObjectRenamerTool : EditorWindow
{
    private string searchName = "";
    private string newName = "";
    private bool exactMatch = false;
    private List<GameObject> foundObjects = new List<GameObject>();

    [MenuItem("Tools/Jrs Object Renamer")]
    public static void ShowWindow()
    {
        GetWindow<JrsObjectRenamerTool>("Object Renamer");
    }

    private Vector2 scrollPosition;
    private string successMessage = "";

    private void OnGUI()
    {
        GUILayout.Label("Search and Rename Objects", EditorStyles.boldLabel);

        // Begin scroll view
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        // Search Section
        EditorGUILayout.BeginVertical("Box");
        GUILayout.Label("Search", EditorStyles.boldLabel);
        searchName = EditorGUILayout.TextField("Search Name", searchName);
        exactMatch = EditorGUILayout.Toggle("Exact Match Only", exactMatch);
        if (GUILayout.Button("Search Objects"))
        {
            SearchObjects();
        }

        // Display success message in a box below Search button
        if (!string.IsNullOrEmpty(successMessage))
        {
            var originalColor = GUI.color;
            GUI.color = Color.green;
            GUILayout.Label(successMessage, EditorStyles.boldLabel);
            GUI.color = originalColor;
        }
        EditorGUILayout.EndVertical();

        if (foundObjects.Count > 0)
        {
            // Results Section
            EditorGUILayout.BeginVertical("Box");
            GUILayout.Label("Found Objects", EditorStyles.boldLabel);
            foreach (var obj in foundObjects)
            {
                EditorGUILayout.ObjectField(obj.name, obj, typeof(GameObject), true);
            }
            EditorGUILayout.EndVertical();

            // Rename Section
            EditorGUILayout.BeginVertical("Box");
            GUILayout.Label("Rename", EditorStyles.boldLabel);
            newName = EditorGUILayout.TextField("New Name", newName);
            if (GUILayout.Button("Rename All"))
            {
                RenameObjects();
            }
            EditorGUILayout.EndVertical();
        }

        // End scroll view
        EditorGUILayout.EndScrollView();

    }

    private void SearchObjects()
    {
        foundObjects.Clear();
        successMessage = "";
        GameObject[] allObjects = Resources.FindObjectsOfTypeAll<GameObject>();
        
        foreach (GameObject obj in allObjects)
        {
            if (exactMatch ? obj.name == searchName : obj.name.Contains(searchName))
            {
                foundObjects.Add(obj);
            }
        }
    }

    private void RenameObjects()
    {
        if (string.IsNullOrEmpty(newName)) return;

        int count = foundObjects.Count;
        foreach (GameObject obj in foundObjects)
        {
            Undo.RecordObject(obj, "Rename Object");
            obj.name = newName;
        }

        successMessage = $"Successfully renamed {count} objects to '{newName}'";
        foundObjects.Clear();
    }
}
