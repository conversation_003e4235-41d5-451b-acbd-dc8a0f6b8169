using UnityEngine;

/// <summary>
/// Test script to verify the multi-door system is working correctly
/// Attach this to any GameObject in the scene to test door functionality
/// </summary>
public class JrsDoorSystemTester : MonoBehaviour
{
    [Header("Test Configuration")]
    public bool enableDebugLogs = true;
    public KeyCode testAllDoorsKey = KeyCode.T;
    
    private JrsNewInputController inputController;
    private JrsDoorMechanic[] doorMechanics;
    
    private void Start()
    {
        // Find the input controller
        inputController = FindObjectOfType<JrsNewInputController>();
        if (inputController == null)
        {
            Debug.LogError("JrsDoorSystemTester: No JrsNewInputController found in scene!");
            return;
        }
        
        // Find all door mechanics
        doorMechanics = FindObjectsOfType<JrsDoorMechanic>();
        if (doorMechanics.Length == 0)
        {
            Debug.LogWarning("JrsDoorSystemTester: No JrsDoorMechanic components found in scene!");
            return;
        }
        
        if (enableDebugLogs)
        {
            Debug.Log($"JrsDoorSystemTester: Found {doorMechanics.Length} door mechanics");
            foreach (var door in doorMechanics)
            {
                Debug.Log($"  - Door: {door.name}, ID: {door.doorID}, Key: {door.toggleKey}");
            }
        }
    }
    
    private void Update()
    {
        if (inputController == null) return;
        
        // Test individual door inputs
        TestIndividualDoorInputs();
        
        // Test all doors at once
        if (Input.GetKeyDown(testAllDoorsKey))
        {
            TestAllDoors();
        }
    }
    
    private void TestIndividualDoorInputs()
    {
        if (!enableDebugLogs) return;
        
        // Test each door ID
        string[] doorIDs = { "Door1", "Door2", "Door3", "Door4", "Door5", "Door6" };
        
        foreach (string doorID in doorIDs)
        {
            if (inputController.GetDoorToggleInput(doorID))
            {
                Debug.Log($"JrsDoorSystemTester: {doorID} input detected!");
            }
        }
        
        // Test generic door toggle
        if (inputController.GetDoorToggleInput())
        {
            Debug.Log("JrsDoorSystemTester: Generic door toggle input detected!");
        }
    }
    
    private void TestAllDoors()
    {
        Debug.Log("JrsDoorSystemTester: Testing all doors...");
        
        foreach (var door in doorMechanics)
        {
            if (door != null)
            {
                door.ToggleDoorPublic();
                Debug.Log($"JrsDoorSystemTester: Toggled door {door.name} (ID: {door.doorID})");
            }
        }
    }
    
    private void OnGUI()
    {
        if (!enableDebugLogs) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 400, 300));
        GUILayout.Label("Door System Tester", GUI.skin.box);
        
        GUILayout.Label($"Input Controller: {(inputController != null ? "Found" : "Missing")}");
        GUILayout.Label($"Door Mechanics: {(doorMechanics != null ? doorMechanics.Length.ToString() : "0")}");
        
        GUILayout.Space(10);
        GUILayout.Label("Controls:");
        GUILayout.Label("1-6: Individual door toggles");
        GUILayout.Label("O: Generic door toggle");
        GUILayout.Label($"{testAllDoorsKey}: Test all doors");
        
        if (doorMechanics != null && doorMechanics.Length > 0)
        {
            GUILayout.Space(10);
            GUILayout.Label("Doors Found:");
            foreach (var door in doorMechanics)
            {
                if (door != null)
                {
                    GUILayout.Label($"  {door.name}: {door.doorID} ({door.toggleKey})");
                }
            }
        }
        
        GUILayout.EndArea();
    }
}
