using UnityEngine;
using UnityEngine.UI;

public class JrsInputController : MonoBehaviour
{
    public JrsCustomButton accelerateButton;
    public JrsCustomButton revButton;
    public JrsCustomButton leftButton;
    public JrsCustomButton rightButton;
    public JrsCustomButton brakeButton;

    public JrsCustomButton headLightsButton;
    public JrsCustomButton sirenButton;

    public JrsCustomButton signalLightsButton;

    public JrsCustomButton extraLightsButton;
    public JrsCustomButton cameraButton;

    public JrsCameraManager cameraManager;

    public float steerSpeed = 2f; // Adjust this value to control the steering speed

    private float verticalInput;
    private float horizontalInput;
    private bool wasCameraButtonPressed = false;

    private void Update()
    {
        // Reset input values
        verticalInput = 0f;

        // Handle acceleration and braking
        if (Input.GetKey(KeyCode.W) || accelerateButton.IsButtonPressed())
        {
            verticalInput = 1f;
            //Debug.Log("Accelerate: verticalInput = " + verticalInput);
        }
        else if (Input.GetKey(KeyCode.S) || revButton.IsButtonPressed())
        {
            verticalInput = -1f;
            //Debug.Log("Brake: verticalInput = " + verticalInput);
        }

        // Handle steering
        float targetHorizontalInput = 0f;
        if (Input.GetKey(KeyCode.A) || leftButton.IsButtonPressed())
        {
            targetHorizontalInput = -1f;
           // Debug.Log("SteerLeft: targetHorizontalInput = " + targetHorizontalInput);
        }
        else if (Input.GetKey(KeyCode.D) || rightButton.IsButtonPressed())
        {
            targetHorizontalInput = 1f;
           // Debug.Log("SteerRight: targetHorizontalInput = " + targetHorizontalInput);
        }

        // Gradually change the horizontalInput value towards the targetHorizontalInput
        horizontalInput = Mathf.MoveTowards(horizontalInput, targetHorizontalInput, steerSpeed * Time.deltaTime);

        // Handle camera switching
        bool isCameraButtonPressed = cameraButton.IsButtonPressed();
        if (isCameraButtonPressed && !wasCameraButtonPressed)
        {
            if (cameraManager != null && cameraManager.cameras.Length > 0)
            {
                // Cycle to next camera
                cameraManager.SwitchToNextCamera();
            }
        }
        wasCameraButtonPressed = isCameraButtonPressed;
    }

    public float GetVerticalInput()
    {
        return verticalInput;
    }

    public float GetHorizontalInput()
    {
        return horizontalInput;
    }
}
