using UnityEngine;

public class JrsDoorMechanic : MonoBehaviour
{
    [Header("Door Settings")]
    public float angle = 90f; // The angle to open the door
    public float speed = 90f; // The speed at which the door opens/closes
    public Axis axisToRotate = Axis.Y; // The axis to rotate the door

    [Header("Input Settings")]
    public string doorID = "Door1"; // Unique identifier for this door (Door1, Door2, Door3, Door4, etc.)

    [System.Obsolete("toggleKey is deprecated. Use doorID with New Input System instead.")]
    [HideInInspector]
    public KeyCode toggleKey = KeyCode.O; // Kept for backward compatibility but hidden

    [Header("Audio")]
    public AudioSource openSound; // AudioSource for the sound when opening the door
    public AudioSource closeSound; // AudioSource for the sound when closing the door

    private bool isOpen = false;
    private bool isRotating = false;
    private Quaternion closedRotation;
    private Quaternion initialRotation; // Store the initial rotation relative to the parent
    private JrsVehicleController vehicleController;
    private JrsVehicleSelector vehicleSelector;
    private JrsNewInputController newInputController; // Reference to new input controller
    private bool useNewInputSystem = false; // Will sync with vehicle controller

    private void Start()
    {
        closedRotation = transform.localRotation;
        initialRotation = transform.localRotation; // Use localRotation instead of eulerAngles
        vehicleController = GetComponentInParent<JrsVehicleController>();
        vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
        newInputController = FindObjectOfType<JrsNewInputController>();

        // Auto-detect input system from vehicle controller
        if (vehicleController != null)
        {
            useNewInputSystem = vehicleController.useNewInputSystem;
        }
    }

    private void Update()
    {
        if (vehicleController == null || !vehicleController.enabled || vehicleSelector == null)
        {
            return;
        }

        // Auto-sync with vehicle controller's input system setting
        if (vehicleController != null)
        {
            useNewInputSystem = vehicleController.useNewInputSystem;
        }

        // Only open/close door if this is the active vehicle
        if (GetDoorToggleInput() && !isRotating && vehicleSelector.GetCurrentVehicle() == vehicleController)
        {
            ToggleDoor();
        }
    }

    // Helper method to get door toggle input from the appropriate input system
    private bool GetDoorToggleInput()
    {
        // Always try New Input System first if available
        if (newInputController != null)
        {
            return newInputController.GetDoorToggleInput(doorID);
        }

        // Fallback to old input system only if New Input System is not available
        if (!useNewInputSystem)
        {
            #pragma warning disable CS0618 // Type or member is obsolete
            return Input.GetKeyDown(toggleKey);
            #pragma warning restore CS0618 // Type or member is obsolete
        }

        return false;
    }

    // Method to handle door toggling logic
    private void ToggleDoor()
    {
        isOpen = !isOpen;
        StopAllCoroutines(); // Stop any ongoing door rotation
        if (isOpen)
        {
            Quaternion targetRotation = GetTargetRotation();
            StartCoroutine(RotateDoor(transform.localRotation, targetRotation));
            PlaySound(openSound);
        }
        else
        {
            StartCoroutine(RotateDoor(transform.localRotation, closedRotation));
            PlaySound(closeSound);
        }
    }

    // Public method to toggle door (can be called from UI buttons)
    public void ToggleDoorPublic()
    {
        if (!isRotating && vehicleSelector != null && vehicleSelector.GetCurrentVehicle() == vehicleController)
        {
            ToggleDoor();
        }
    }

    private Quaternion GetTargetRotation()
    {
        Vector3 targetEulerAngles = initialRotation.eulerAngles;
        switch (axisToRotate)
        {
            case Axis.X:
                targetEulerAngles.x += angle;
                break;
            case Axis.Y:
                targetEulerAngles.y += angle;
                break;
            case Axis.Z:
                targetEulerAngles.z += angle;
                break;
        }
        return Quaternion.Euler(targetEulerAngles);
    }

    private System.Collections.IEnumerator RotateDoor(Quaternion startRotation, Quaternion targetRotation)
    {
        isRotating = true;
        float t = 0f;
        while (t < 1f)
        {
            t += Time.deltaTime * speed;
            transform.localRotation = Quaternion.Lerp(startRotation, targetRotation, t);
            yield return null;
        }
        isRotating = false;
    }

    private void PlaySound(AudioSource sound)
    {
        if (sound != null)
        {
            sound.PlayOneShot(sound.clip);
        }
    }
}

public enum Axis
{
    X,
    Y,
    Z
}
