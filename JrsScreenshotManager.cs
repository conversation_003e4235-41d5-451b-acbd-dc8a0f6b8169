using UnityEngine;
using System.IO;

public class JrsScreenshotManager : MonoBehaviour
{
    public string screenshotPath = "E:\\";

    public KeyCode screenshotKey = KeyCode.F1; // Custom key for taking a screenshot

    private int screenshotCount = 1;

    private void Update()
    {
        if (Input.GetKeyDown(screenshotKey))
        {
            string screenshotName = "Screenshot_" + screenshotCount.ToString() + ".png";
            string fullPath = Path.Combine(screenshotPath, screenshotName); // Use Path.Combine to handle path concatenation

            // Check if the file already exists
            while (File.Exists(fullPath))
            {
                screenshotCount++;
                screenshotName = "Screenshot_" + screenshotCount.ToString() + ".png";
                fullPath = Path.Combine(screenshotPath, screenshotName);
            }

            ScreenCapture.CaptureScreenshot(fullPath);
            UnityEngine.Debug.Log("Screenshot saved at: " + fullPath);

            screenshotCount++;
        }
    }
}
