using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// Comprehensive validator for all input system integrations
/// Tests lights, camera switching, door controls, and siren functionality
/// </summary>
public class JrsInputSystemValidator : MonoBehaviour
{
    [Header("Validation Settings")]
    public bool runValidationOnStart = true;
    public bool showDetailedLogs = true;
    public bool enableRealTimeMonitoring = true;
    
    [Header("System References")]
    public JrsNewInputController newInputController;
    public JrsVehicleLightControl lightControl;
    public JrsCameraManager cameraManager;
    public JrsPoliceSiren policeSiren;
    
    [Header("Validation Results")]
    [SerializeField] private bool inputSystemValid = false;
    [SerializeField] private bool lightSystemValid = false;
    [SerializeField] private bool cameraSystemValid = false;
    [SerializeField] private bool doorSystemValid = false;
    [SerializeField] private bool sirenSystemValid = false;
    
    private VehicleInputActions testInputActions;
    
    private void Start()
    {
        FindSystemReferences();
        
        if (runValidationOnStart)
        {
            ValidateAllSystems();
        }
    }
    
    private void Update()
    {
        if (enableRealTimeMonitoring)
        {
            MonitorInputs();
        }
    }
    
    private void FindSystemReferences()
    {
        if (newInputController == null)
            newInputController = FindObjectOfType<JrsNewInputController>();
            
        if (lightControl == null)
            lightControl = FindObjectOfType<JrsVehicleLightControl>();
            
        if (cameraManager == null)
            cameraManager = FindObjectOfType<JrsCameraManager>();
            
        if (policeSiren == null)
            policeSiren = FindObjectOfType<JrsPoliceSiren>();
    }
    
    [ContextMenu("Validate All Systems")]
    public void ValidateAllSystems()
    {
        Debug.Log("=== Input System Validation Started ===");
        
        ValidateInputActions();
        ValidateLightSystem();
        ValidateCameraSystem();
        ValidateDoorSystem();
        ValidateSirenSystem();
        
        bool allSystemsValid = inputSystemValid && lightSystemValid && cameraSystemValid && doorSystemValid && sirenSystemValid;
        
        Debug.Log($"=== Input System Validation Complete ===");
        Debug.Log($"Overall Status: {(allSystemsValid ? "✅ ALL SYSTEMS VALID" : "❌ SOME SYSTEMS INVALID")}");
        
        if (!allSystemsValid)
        {
            Debug.LogWarning("Please check the validation results above and fix any issues.");
        }
    }
    
    private void ValidateInputActions()
    {
        Debug.Log("--- Validating Input Actions ---");
        
        try
        {
            testInputActions = new VehicleInputActions();
            
            bool hasHeadLights = testInputActions.Vehicle.HeadLights != null;
            bool hasSignalLights = testInputActions.Vehicle.SignalLights != null;
            bool hasExtraLights = testInputActions.Vehicle.ExtraLights != null;
            bool hasSiren = testInputActions.Vehicle.Siren != null;
            bool hasSwitchCamera = testInputActions.Vehicle.SwitchCamera != null;
            bool hasDoorToggle = testInputActions.Vehicle.DoorToggle != null;
            
            inputSystemValid = hasHeadLights && hasSignalLights && hasExtraLights && hasSiren && hasSwitchCamera && hasDoorToggle;
            
            if (showDetailedLogs)
            {
                Debug.Log($"  HeadLights Action: {(hasHeadLights ? "✅" : "❌")}");
                Debug.Log($"  SignalLights Action: {(hasSignalLights ? "✅" : "❌")}");
                Debug.Log($"  ExtraLights Action: {(hasExtraLights ? "✅" : "❌")}");
                Debug.Log($"  Siren Action: {(hasSiren ? "✅" : "❌")}");
                Debug.Log($"  SwitchCamera Action: {(hasSwitchCamera ? "✅" : "❌")}");
                Debug.Log($"  DoorToggle Action: {(hasDoorToggle ? "✅" : "❌")}");
            }
            
            testInputActions.Dispose();
            
            Debug.Log($"Input Actions: {(inputSystemValid ? "✅ VALID" : "❌ INVALID")}");
        }
        catch (System.Exception e)
        {
            inputSystemValid = false;
            Debug.LogError($"Input Actions Validation Failed: {e.Message}");
        }
    }
    
    private void ValidateLightSystem()
    {
        Debug.Log("--- Validating Light System ---");
        
        bool hasNewInputController = newInputController != null;
        bool hasLightControl = lightControl != null;
        bool hasLightMethods = false;
        
        if (hasNewInputController)
        {
            try
            {
                // Test if the new input methods exist
                newInputController.GetHeadLightsInput();
                newInputController.GetSignalLightsInput();
                newInputController.GetExtraLightsInput();
                newInputController.GetSirenInput();
                hasLightMethods = true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Light input methods missing: {e.Message}");
            }
        }
        
        lightSystemValid = hasNewInputController && hasLightControl && hasLightMethods;
        
        if (showDetailedLogs)
        {
            Debug.Log($"  New Input Controller: {(hasNewInputController ? "✅" : "❌")}");
            Debug.Log($"  Light Control: {(hasLightControl ? "✅" : "❌")}");
            Debug.Log($"  Light Input Methods: {(hasLightMethods ? "✅" : "❌")}");
        }
        
        Debug.Log($"Light System: {(lightSystemValid ? "✅ VALID" : "❌ INVALID")}");
    }
    
    private void ValidateCameraSystem()
    {
        Debug.Log("--- Validating Camera System ---");
        
        bool hasCameraManager = cameraManager != null;
        bool hasCameras = hasCameraManager && cameraManager.cameras.Length > 0;
        bool hasValidCameras = false;
        
        if (hasCameras)
        {
            int validCameraCount = 0;
            foreach (var camera in cameraManager.cameras)
            {
                if (camera != null) validCameraCount++;
            }
            hasValidCameras = validCameraCount > 0;
        }
        
        cameraSystemValid = hasCameraManager && hasCameras && hasValidCameras;
        
        if (showDetailedLogs)
        {
            Debug.Log($"  Camera Manager: {(hasCameraManager ? "✅" : "❌")}");
            Debug.Log($"  Has Cameras: {(hasCameras ? "✅" : "❌")}");
            Debug.Log($"  Valid Cameras: {(hasValidCameras ? "✅" : "❌")}");
            if (hasCameraManager)
            {
                Debug.Log($"  Camera Count: {cameraManager.cameras.Length}");
            }
        }
        
        Debug.Log($"Camera System: {(cameraSystemValid ? "✅ VALID" : "❌ INVALID")}");
    }
    
    private void ValidateDoorSystem()
    {
        Debug.Log("--- Validating Door System ---");
        
        var doorMechanics = FindObjectsOfType<JrsDoorMechanic>();
        bool hasDoorsWithValidIDs = false;
        
        if (doorMechanics.Length > 0)
        {
            int validDoorCount = 0;
            foreach (var door in doorMechanics)
            {
                if (!string.IsNullOrEmpty(door.doorID))
                {
                    validDoorCount++;
                }
            }
            hasDoorsWithValidIDs = validDoorCount > 0;
        }
        
        doorSystemValid = hasDoorsWithValidIDs;
        
        if (showDetailedLogs)
        {
            Debug.Log($"  Door Mechanics Found: {doorMechanics.Length}");
            Debug.Log($"  Doors with Valid IDs: {(hasDoorsWithValidIDs ? "✅" : "❌")}");
        }
        
        Debug.Log($"Door System: {(doorSystemValid ? "✅ VALID" : "❌ INVALID")}");
    }
    
    private void ValidateSirenSystem()
    {
        Debug.Log("--- Validating Siren System ---");
        
        bool hasPoliceSiren = policeSiren != null;
        bool hasSirenInput = newInputController != null;
        
        sirenSystemValid = hasPoliceSiren && hasSirenInput;
        
        if (showDetailedLogs)
        {
            Debug.Log($"  Police Siren: {(hasPoliceSiren ? "✅" : "❌")}");
            Debug.Log($"  Siren Input Available: {(hasSirenInput ? "✅" : "❌")}");
        }
        
        Debug.Log($"Siren System: {(sirenSystemValid ? "✅ VALID" : "❌ INVALID")}");
    }
    
    private void MonitorInputs()
    {
        if (newInputController == null) return;
        
        // Monitor light inputs
        if (newInputController.GetHeadLightsInput())
            Debug.Log("🔦 HeadLights Input Detected!");
            
        if (newInputController.GetSignalLightsInput())
            Debug.Log("🚨 Signal Lights Input Detected!");
            
        if (newInputController.GetExtraLightsInput())
            Debug.Log("💡 Extra Lights Input Detected!");
            
        if (newInputController.GetSirenInput())
            Debug.Log("🚔 Siren Input Detected!");
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, Screen.height - 200, 300, 190));
        GUILayout.Label("Input System Status", GUI.skin.box);
        
        GUI.color = inputSystemValid ? Color.green : Color.red;
        GUILayout.Label($"Input Actions: {(inputSystemValid ? "✅" : "❌")}");
        
        GUI.color = lightSystemValid ? Color.green : Color.red;
        GUILayout.Label($"Light System: {(lightSystemValid ? "✅" : "❌")}");
        
        GUI.color = cameraSystemValid ? Color.green : Color.red;
        GUILayout.Label($"Camera System: {(cameraSystemValid ? "✅" : "❌")}");
        
        GUI.color = doorSystemValid ? Color.green : Color.red;
        GUILayout.Label($"Door System: {(doorSystemValid ? "✅" : "❌")}");
        
        GUI.color = sirenSystemValid ? Color.green : Color.red;
        GUILayout.Label($"Siren System: {(sirenSystemValid ? "✅" : "❌")}");
        
        GUI.color = Color.white;
        
        if (GUILayout.Button("Validate All Systems"))
        {
            ValidateAllSystems();
        }
        
        GUILayout.EndArea();
    }
    
    private void OnDestroy()
    {
        if (testInputActions != null)
        {
            testInputActions.Dispose();
        }
    }
}
