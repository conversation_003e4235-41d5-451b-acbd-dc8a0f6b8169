using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// Validation script to ensure the multi-door system is properly configured
/// This script will check all components and report any issues
/// </summary>
public class JrsDoorSystemValidator : MonoBehaviour
{
    [Header("Validation Settings")]
    public bool runValidationOnStart = true;
    public bool showDetailedLogs = true;
    
    [Header("Validation Results")]
    [SerializeField] private bool inputActionsValid = false;
    [SerializeField] private bool inputControllerValid = false;
    [SerializeField] private bool doorMechanicsValid = false;
    [SerializeField] private int doorsFound = 0;
    
    private void Start()
    {
        if (runValidationOnStart)
        {
            ValidateSystem();
        }
    }
    
    [ContextMenu("Validate Door System")]
    public void ValidateSystem()
    {
        Debug.Log("=== Door System Validation Started ===");
        
        ValidateInputActions();
        ValidateInputController();
        ValidateDoorMechanics();
        
        bool systemValid = inputActionsValid && inputControllerValid && doorMechanicsValid;
        
        Debug.Log($"=== Door System Validation Complete ===");
        Debug.Log($"System Status: {(systemValid ? "✅ VALID" : "❌ INVALID")}");
        
        if (!systemValid)
        {
            Debug.LogWarning("Please check the validation results above and fix any issues.");
        }
    }
    
    private void ValidateInputActions()
    {
        Debug.Log("--- Validating Input Actions ---");
        
        try
        {
            var inputActions = new VehicleInputActions();
            
            // Check if all door actions exist
            bool door1Exists = inputActions.Vehicle.Door1Toggle != null;
            bool door2Exists = inputActions.Vehicle.Door2Toggle != null;
            bool door3Exists = inputActions.Vehicle.Door3Toggle != null;
            bool door4Exists = inputActions.Vehicle.Door4Toggle != null;
            bool door5Exists = inputActions.Vehicle.Door5Toggle != null;
            bool door6Exists = inputActions.Vehicle.Door6Toggle != null;
            bool genericExists = inputActions.Vehicle.DoorToggle != null;
            
            inputActionsValid = door1Exists && door2Exists && door3Exists && door4Exists && genericExists;
            
            if (showDetailedLogs)
            {
                Debug.Log($"  Generic Door Toggle: {(genericExists ? "✅" : "❌")}");
                Debug.Log($"  Door1 Toggle: {(door1Exists ? "✅" : "❌")}");
                Debug.Log($"  Door2 Toggle: {(door2Exists ? "✅" : "❌")}");
                Debug.Log($"  Door3 Toggle: {(door3Exists ? "✅" : "❌")}");
                Debug.Log($"  Door4 Toggle: {(door4Exists ? "✅" : "❌")}");
                Debug.Log($"  Door5 Toggle: {(door5Exists ? "✅" : "❌")}");
                Debug.Log($"  Door6 Toggle: {(door6Exists ? "✅" : "❌")}");
            }
            
            inputActions.Dispose();
            
            Debug.Log($"Input Actions: {(inputActionsValid ? "✅ VALID" : "❌ INVALID")}");
        }
        catch (System.Exception e)
        {
            inputActionsValid = false;
            Debug.LogError($"Input Actions Validation Failed: {e.Message}");
        }
    }
    
    private void ValidateInputController()
    {
        Debug.Log("--- Validating Input Controller ---");
        
        var inputController = FindObjectOfType<JrsNewInputController>();
        
        if (inputController == null)
        {
            inputControllerValid = false;
            Debug.LogError("  ❌ JrsNewInputController not found in scene!");
            return;
        }
        
        // Check if the controller has the necessary methods
        bool hasGenericMethod = true;
        bool hasSpecificMethod = true;
        
        try
        {
            // Test the methods exist (this will compile-time check)
            inputController.GetDoorToggleInput();
            inputController.GetDoorToggleInput("Door1");
        }
        catch (System.Exception e)
        {
            hasGenericMethod = false;
            hasSpecificMethod = false;
            Debug.LogError($"  ❌ Input Controller methods missing: {e.Message}");
        }
        
        inputControllerValid = hasGenericMethod && hasSpecificMethod;
        
        if (showDetailedLogs)
        {
            Debug.Log($"  Controller Found: ✅");
            Debug.Log($"  Generic Method: {(hasGenericMethod ? "✅" : "❌")}");
            Debug.Log($"  Specific Method: {(hasSpecificMethod ? "✅" : "❌")}");
        }
        
        Debug.Log($"Input Controller: {(inputControllerValid ? "✅ VALID" : "❌ INVALID")}");
    }
    
    private void ValidateDoorMechanics()
    {
        Debug.Log("--- Validating Door Mechanics ---");
        
        var doorMechanics = FindObjectsOfType<JrsDoorMechanic>();
        doorsFound = doorMechanics.Length;
        
        if (doorsFound == 0)
        {
            doorMechanicsValid = false;
            Debug.LogWarning("  ⚠️ No JrsDoorMechanic components found in scene!");
            return;
        }
        
        bool allDoorsValid = true;
        
        foreach (var door in doorMechanics)
        {
            bool doorValid = ValidateSingleDoor(door);
            if (!doorValid) allDoorsValid = false;
        }
        
        doorMechanicsValid = allDoorsValid;
        
        Debug.Log($"Doors Found: {doorsFound}");
        Debug.Log($"Door Mechanics: {(doorMechanicsValid ? "✅ VALID" : "❌ INVALID")}");
    }
    
    private bool ValidateSingleDoor(JrsDoorMechanic door)
    {
        if (door == null) return false;
        
        bool hasValidID = !string.IsNullOrEmpty(door.doorID);
        bool hasValidKey = door.toggleKey != KeyCode.None;
        
        if (showDetailedLogs)
        {
            Debug.Log($"  Door: {door.name}");
            Debug.Log($"    ID: {door.doorID} {(hasValidID ? "✅" : "❌")}");
            Debug.Log($"    Key: {door.toggleKey} {(hasValidKey ? "✅" : "❌")}");
        }
        
        return hasValidID && hasValidKey;
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Screen.width - 250, 10, 240, 200));
        GUILayout.Label("Door System Status", GUI.skin.box);
        
        GUI.color = inputActionsValid ? Color.green : Color.red;
        GUILayout.Label($"Input Actions: {(inputActionsValid ? "✅" : "❌")}");
        
        GUI.color = inputControllerValid ? Color.green : Color.red;
        GUILayout.Label($"Input Controller: {(inputControllerValid ? "✅" : "❌")}");
        
        GUI.color = doorMechanicsValid ? Color.green : Color.red;
        GUILayout.Label($"Door Mechanics: {(doorMechanicsValid ? "✅" : "❌")}");
        
        GUI.color = Color.white;
        GUILayout.Label($"Doors Found: {doorsFound}");
        
        if (GUILayout.Button("Validate System"))
        {
            ValidateSystem();
        }
        
        GUILayout.EndArea();
    }
}
