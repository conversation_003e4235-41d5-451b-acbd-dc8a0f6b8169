using UnityEngine;
using System.Collections.Generic;

public class JrsScreenDisplayManager : MonoBehaviour
{
    [Header("Vehicle Reference")]
    public Transform vehicleTransform;

    [System.Serializable]
    public struct CameraData
    {
        public Camera displayCam;
        public Vector3 camOffset;
        public Vector3 camRotationOffset;
        public Material screenMaterial;
    }

    [Header("Display Cameras")]
    public List<CameraData> cameras = new List<CameraData>();

    [Header("Display Adjustments")]
    [Range(0.5f, 2f)] public float brightness = 1f;
    [Range(0.5f, 2f)] public float contrast = 1f;
    [Range(0f, 1f)] public float saturation = 1f;

    private List<RenderTexture> renderTextures = new List<RenderTexture>();

    private void Start()
    {
        if (vehicleTransform == null)
        {
            Debug.LogError("Vehicle transform not assigned!");
            return;
        }

        // Create render textures and setup cameras
        foreach (var camData in cameras)
        {
            RenderTexture rt = CreateRenderTexture(camData.displayCam.name + "RT");
            renderTextures.Add(rt);
            SetupDisplayCamera(camData.displayCam, rt, camData.screenMaterial);
        }
    }

    private void LateUpdate()
    {
        if (vehicleTransform == null) return;

        // Update camera positions relative to vehicle
        foreach (var camData in cameras)
        {
            UpdateCameraPosition(camData);
        }

        // Update display properties
        UpdateDisplayProperties();
    }

    private void UpdateDisplayProperties()
    {
        foreach (var camData in cameras)
        {
            UpdateMaterialProperties(camData.screenMaterial);
        }
    }

    private void UpdateMaterialProperties(Material mat)
    {
        if (mat != null && mat.shader.name == "Custom/JrsScreenShader")
        {
            mat.SetFloat("_Brightness", brightness);
            mat.SetFloat("_Contrast", contrast);
            mat.SetFloat("_Saturation", saturation);
            
            // Debug log to verify values
            Debug.Log($"Updating material: Brightness={brightness}, Contrast={contrast}, Saturation={saturation}");
        }
        else if (mat != null)
        {
            Debug.LogWarning($"Material {mat.name} is not using JrsScreenShader");
        }
    }

    private void UpdateCameraPosition(CameraData camData)
    {
        if (camData.displayCam != null)
        {
            // Update position relative to vehicle
            camData.displayCam.transform.position = vehicleTransform.TransformPoint(camData.camOffset);
            
            // Apply vehicle rotation while maintaining local rotation offset
            camData.displayCam.transform.rotation = vehicleTransform.rotation * Quaternion.Euler(camData.camRotationOffset);
        }
    }

    private RenderTexture CreateRenderTexture(string name)
    {
        RenderTexture rt = new RenderTexture(1024, 768, 24);
        rt.name = name;
        return rt;
    }

    private void SetupDisplayCamera(Camera cam, RenderTexture rt, Material screenMat)
    {
        if (cam != null)
        {
            cam.targetTexture = rt;
            cam.fieldOfView = 90f;
            
            if (screenMat != null)
            {
                screenMat.mainTexture = rt;
            }
        }
    }

    private void OnDestroy()
    {
        // Clean up render textures
        foreach (var rt in renderTextures)
        {
            if (rt)
            {
                rt.Release();
            }
        }
        renderTextures.Clear();
    }
}
