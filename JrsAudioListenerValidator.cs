using UnityEngine;
using System.Collections;

/// <summary>
/// Comprehensive validator for AudioListener management in the camera system
/// Tests that audio properly follows camera switches and maintains 3D positioning
/// </summary>
public class JrsAudioListenerValidator : MonoBehaviour
{
    [Header("Validation Settings")]
    public bool runValidationOnStart = true;
    public bool showDetailedLogs = true;
    public bool enableRealTimeMonitoring = true;
    
    [Header("System References")]
    public JrsCameraManager cameraManager;
    public JrsVehicleController vehicleController;
    
    [Header("Test Results")]
    public bool audioListenerSystemValid = false;
    public bool cameraAudioSyncValid = false;
    public bool vehicleAudioPositioningValid = false;
    
    private AudioListener[] allAudioListeners;
    private int lastActiveCameraIndex = -1;
    
    private void Start()
    {
        if (runValidationOnStart)
        {
            StartCoroutine(DelayedValidation());
        }
    }
    
    private void Update()
    {
        if (enableRealTimeMonitoring)
        {
            MonitorAudioListenerState();
        }
    }
    
    private IEnumerator DelayedValidation()
    {
        // Wait a frame to ensure all systems are initialized
        yield return new WaitForEndOfFrame();
        
        FindSystemReferences();
        ValidateAudioListenerSystem();
    }
    
    private void FindSystemReferences()
    {
        if (cameraManager == null)
            cameraManager = FindObjectOfType<JrsCameraManager>();
            
        if (vehicleController == null)
            vehicleController = FindObjectOfType<JrsVehicleController>();
    }
    
    private void ValidateAudioListenerSystem()
    {
        Debug.Log("=== AUDIO LISTENER VALIDATION START ===");
        
        ValidateAudioListenerSetup();
        ValidateCameraAudioSync();
        ValidateVehicleAudioPositioning();
        
        bool overallValid = audioListenerSystemValid && cameraAudioSyncValid && vehicleAudioPositioningValid;
        
        Debug.Log($"=== AUDIO LISTENER VALIDATION COMPLETE ===");
        Debug.Log($"Overall Audio System: {(overallValid ? "✅ VALID" : "❌ INVALID")}");
        
        if (!overallValid)
        {
            Debug.LogWarning("Audio system validation failed! Check individual test results above.");
        }
    }
    
    private void ValidateAudioListenerSetup()
    {
        Debug.Log("--- Validating AudioListener Setup ---");
        
        if (cameraManager == null || cameraManager.cameras == null)
        {
            Debug.LogError("❌ CameraManager or cameras array is null!");
            audioListenerSystemValid = false;
            return;
        }
        
        allAudioListeners = FindObjectsOfType<AudioListener>();
        int enabledListeners = 0;
        int totalCameraListeners = 0;
        
        foreach (Camera cam in cameraManager.cameras)
        {
            if (cam == null) continue;
            
            AudioListener listener = cam.GetComponent<AudioListener>();
            if (listener != null)
            {
                totalCameraListeners++;
                if (listener.enabled)
                {
                    enabledListeners++;
                    if (showDetailedLogs)
                        Debug.Log($"  ✅ Active AudioListener on: {cam.name}");
                }
                else
                {
                    if (showDetailedLogs)
                        Debug.Log($"  ⚪ Inactive AudioListener on: {cam.name}");
                }
            }
            else
            {
                Debug.LogWarning($"  ❌ Missing AudioListener on camera: {cam.name}");
            }
        }
        
        // Validation criteria
        bool hasCorrectListenerCount = (enabledListeners == 1);
        bool allCamerasHaveListeners = (totalCameraListeners == cameraManager.cameras.Length);
        
        audioListenerSystemValid = hasCorrectListenerCount && allCamerasHaveListeners;
        
        if (showDetailedLogs)
        {
            Debug.Log($"  Total AudioListeners in scene: {allAudioListeners.Length}");
            Debug.Log($"  Enabled AudioListeners: {enabledListeners}");
            Debug.Log($"  Camera AudioListeners: {totalCameraListeners}/{cameraManager.cameras.Length}");
        }
        
        Debug.Log($"AudioListener Setup: {(audioListenerSystemValid ? "✅ VALID" : "❌ INVALID")}");
        
        if (!hasCorrectListenerCount)
        {
            Debug.LogError($"❌ Expected exactly 1 enabled AudioListener, found {enabledListeners}");
        }
    }
    
    private void ValidateCameraAudioSync()
    {
        Debug.Log("--- Validating Camera-Audio Synchronization ---");
        
        if (cameraManager == null)
        {
            cameraAudioSyncValid = false;
            return;
        }
        
        // Find the currently active camera
        Camera activeCamera = null;
        AudioListener activeListener = null;
        
        for (int i = 0; i < cameraManager.cameras.Length; i++)
        {
            if (cameraManager.cameras[i] != null && cameraManager.cameras[i].gameObject.activeInHierarchy)
            {
                activeCamera = cameraManager.cameras[i];
                activeListener = activeCamera.GetComponent<AudioListener>();
                break;
            }
        }
        
        bool hasActiveCamera = (activeCamera != null);
        bool hasActiveListener = (activeListener != null && activeListener.enabled);
        bool listenerOnActiveCamera = hasActiveCamera && hasActiveListener;
        
        cameraAudioSyncValid = listenerOnActiveCamera;
        
        if (showDetailedLogs)
        {
            Debug.Log($"  Active Camera: {(hasActiveCamera ? activeCamera.name : "None")}");
            Debug.Log($"  AudioListener on Active Camera: {(hasActiveListener ? "✅" : "❌")}");
        }
        
        Debug.Log($"Camera-Audio Sync: {(cameraAudioSyncValid ? "✅ VALID" : "❌ INVALID")}");
    }
    
    private void ValidateVehicleAudioPositioning()
    {
        Debug.Log("--- Validating Vehicle Audio Positioning ---");
        
        if (vehicleController == null)
        {
            Debug.LogWarning("❌ VehicleController not found - skipping audio positioning test");
            vehicleAudioPositioningValid = false;
            return;
        }
        
        bool hasEngineAudio = (vehicleController.engineAudioSource != null);
        bool hasStartAudio = (vehicleController.engineStartAudioSource != null);
        
        vehicleAudioPositioningValid = hasEngineAudio && hasStartAudio;
        
        if (showDetailedLogs)
        {
            Debug.Log($"  Engine AudioSource: {(hasEngineAudio ? "✅" : "❌")}");
            Debug.Log($"  Start AudioSource: {(hasStartAudio ? "✅" : "❌")}");
            
            if (hasEngineAudio)
            {
                Vector3 enginePos = vehicleController.engineAudioSource.transform.position;
                Debug.Log($"  Engine Audio Position: {enginePos}");
            }
        }
        
        Debug.Log($"Vehicle Audio Positioning: {(vehicleAudioPositioningValid ? "✅ VALID" : "❌ INVALID")}");
    }
    
    private void MonitorAudioListenerState()
    {
        if (cameraManager == null) return;
        
        // Check if active camera has changed
        int currentActiveCameraIndex = -1;
        for (int i = 0; i < cameraManager.cameras.Length; i++)
        {
            if (cameraManager.cameras[i] != null && cameraManager.cameras[i].gameObject.activeInHierarchy)
            {
                currentActiveCameraIndex = i;
                break;
            }
        }
        
        if (currentActiveCameraIndex != lastActiveCameraIndex && currentActiveCameraIndex != -1)
        {
            lastActiveCameraIndex = currentActiveCameraIndex;
            
            if (showDetailedLogs)
            {
                Debug.Log($"🎥 Camera switched to: {cameraManager.cameras[currentActiveCameraIndex].name}");
                
                // Verify AudioListener is properly set
                AudioListener listener = cameraManager.cameras[currentActiveCameraIndex].GetComponent<AudioListener>();
                if (listener != null && listener.enabled)
                {
                    Debug.Log($"🔊 AudioListener active on: {cameraManager.cameras[currentActiveCameraIndex].name}");
                }
                else
                {
                    Debug.LogWarning($"⚠️ AudioListener NOT active on current camera!");
                }
            }
        }
    }
    
    /// <summary>
    /// Manual test method to simulate camera switching and validate audio
    /// </summary>
    [ContextMenu("Test Camera Audio Switching")]
    public void TestCameraAudioSwitching()
    {
        if (cameraManager == null)
        {
            Debug.LogError("CameraManager not assigned!");
            return;
        }
        
        Debug.Log("🧪 Starting Camera Audio Switching Test...");
        StartCoroutine(CameraAudioSwitchingTest());
    }
    
    private IEnumerator CameraAudioSwitchingTest()
    {
        for (int i = 0; i < cameraManager.cameras.Length; i++)
        {
            Debug.Log($"🎥 Testing camera {i}: {cameraManager.cameras[i].name}");
            
            // Switch to camera
            cameraManager.SwitchToNextCamera();
            
            yield return new WaitForSeconds(1f);
            
            // Validate audio listener
            AudioListener listener = cameraManager.cameras[i].GetComponent<AudioListener>();
            bool isValid = (listener != null && listener.enabled);
            
            Debug.Log($"  AudioListener Status: {(isValid ? "✅ Active" : "❌ Inactive")}");
            
            yield return new WaitForSeconds(2f);
        }
        
        Debug.Log("🧪 Camera Audio Switching Test Complete!");
    }
}
