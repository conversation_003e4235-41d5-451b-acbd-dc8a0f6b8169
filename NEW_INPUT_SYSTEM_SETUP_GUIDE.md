# Unity New Input System Setup Guide for Vehicle Controller

## Overview
This guide will help you set up Unity's New Input System to work with your vehicle controller while maintaining compatibility with your existing mobile touch controls.

## Step 1: Install the Input System Package

1. **Open Package Manager**:
   - Go to `Window` → `Package Manager`
   - In the dropdown at the top left, select `Unity Registry`

2. **Install Input System**:
   - Search for "Input System"
   - Click on "Input System" by Unity Technologies
   - Click `Install`

3. **Handle the Warning**:
   - Unity will show a warning about enabling the new Input System
   - Click `Yes` to enable it
   - Unity will restart automatically

## Step 2: Configure Project Settings

1. **Open Project Settings**:
   - Go to `Edit` → `Project Settings`
   - Navigate to `XR Plug-in Management` → `Input System Package`

2. **Set Active Input Handling**:
   - Find "Active Input Handling" setting
   - Change it to `Both` (this allows old and new input systems to work together)
   - This ensures compatibility with your existing mobile controls

## Step 3: Set Up the Input Action Asset

1. **Import the Input Actions File**:
   - The `VehicleInputActions.inputactions` file has been created for you
   - Make sure it's in your project folder

2. **Configure the Input Actions**:
   - Double-click on `VehicleInputActions.inputactions` to open the Input Actions editor
   - You'll see all the vehicle controls already configured:
     - **Accelerate**: W key, Up Arrow
     - **Reverse**: S key, Down Arrow  
     - **Steer**: A/D keys, Left/Right Arrows
     - **HandBrake**: Space key
     - **HeadLights**: H key
     - **Siren**: P key
     - **SignalLights**: L key
     - **ExtraLights**: E key
     - **SwitchCamera**: C key

3. **Generate C# Class**:
   - In the Input Actions window, click the `Generate C# Class` checkbox
   - Set the file path to your script folder
   - Click `Apply` to generate the C# wrapper class

## Step 4: Set Up Your Vehicle GameObject

### Option A: Using New Input System Only

1. **Add New Input Controller**:
   - Select your vehicle GameObject
   - Add the `JrsNewInputController` component
   - Remove or disable the old `JrsInputController` component

2. **Configure the New Input Controller**:
   - The `Vehicle Input Actions` field will be automatically populated
   - Assign your mobile UI buttons if you want hybrid support:
     - Accelerate Button
     - Rev Button  
     - Left Button
     - Right Button
     - Brake Button
     - Camera Button
     - Light control buttons
   - Assign the Camera Manager
   - Set `Enable Mobile Controls` to `true` if you want touch support

3. **Update Vehicle Controller**:
   - In your `JrsVehicleController` component
   - Check the `Use New Input System` checkbox

### Option B: Hybrid Setup (Both Systems)

1. **Keep Both Controllers**:
   - Keep your existing `JrsInputController` component
   - Add the `JrsNewInputController` component

2. **Configure Vehicle Controller**:
   - You can toggle between input systems using the `Use New Input System` checkbox
   - This allows you to switch at runtime or test both systems

## Step 5: Testing Your Setup

### Keyboard Testing:
- **W** or **Up Arrow**: Accelerate
- **S** or **Down Arrow**: Reverse
- **A/D** or **Left/Right Arrows**: Steer
- **Space**: Hand brake
- **H**: Toggle headlights
- **P**: Toggle siren
- **L**: Toggle signal lights
- **E**: Toggle extra lights
- **C**: Switch camera

### Mobile Testing:
- Your existing touch buttons should still work if `Enable Mobile Controls` is checked
- The new input system will work alongside your touch controls

## Step 6: Advanced Configuration

### Customizing Key Bindings:
1. Open `VehicleInputActions.inputactions`
2. Select any action (e.g., "Accelerate")
3. In the right panel, you can add/remove/modify key bindings
4. Click `Save Asset` when done

### Adding Gamepad Support:
1. In the Input Actions editor, select an action
2. Click the `+` button next to "Bindings"
3. Choose gamepad controls (e.g., `<Gamepad>/leftStick/x` for steering)

### Runtime Input System Switching:
```csharp
// Switch to new input system
vehicleController.SetInputSystem(true);

// Switch to old input system  
vehicleController.SetInputSystem(false);
```

## Troubleshooting

### Common Issues:

1. **"Input System package not found" error**:
   - Make sure you installed the Input System package
   - Restart Unity after installation

2. **Controls not responding**:
   - Check that `Use New Input System` is enabled in Vehicle Controller
   - Verify the Input Actions asset is assigned
   - Make sure the Input Actions are enabled in play mode

3. **Mobile controls not working**:
   - Ensure `Enable Mobile Controls` is checked
   - Verify your UI buttons are properly assigned
   - Check that your mobile UI Canvas is set up correctly

4. **Both systems interfering**:
   - Use only one input controller at a time
   - Or use the hybrid setup with proper toggling

### Debug Tips:
- Check the Console for input-related debug messages
- Use `Debug.Log()` in the input controller to verify input values
- Test in both Editor and Build to ensure consistency

## Benefits of the New Input System

1. **Better Performance**: More efficient input handling
2. **Gamepad Support**: Easy to add controller support
3. **Rebindable Controls**: Players can customize their controls
4. **Multi-platform**: Better support across different platforms
5. **Event-driven**: More responsive input handling

## Next Steps

Once everything is working:
1. Test thoroughly on your target platforms
2. Consider adding gamepad support for better accessibility
3. Implement control remapping for player customization
4. Add input buffering for more responsive controls

Your vehicle controller now supports both the old and new input systems, giving you flexibility and future-proofing your project!
