<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Complete guide to implementing vehicle physics in Unity with lights, sounds, and animations. Learn how to create realistic vehicle controls and mechanics.">
    <meta name="keywords" content="Unity, Vehicle Physics, Game Development, Car Controller, Unity3D, Game Programming, Vehicle Controls">
    <title>Complete Guide to Unity Vehicle Physics Implementation</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #3b82f6;
            --text-color: #1f2937;
            --background-color: #ffffff;
            --card-background: #f8fafc;
            --tip-background: #e0f2fe;
            --warning-background: #fef3c7;
            --note-background: #f3e8ff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            color: var(--primary-color);
            text-align: center;
        }

        h2 {
            font-size: 2rem;
            margin: 2rem 0 1rem;
            color: var(--secondary-color);
        }

        h3 {
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem;
            color: var(--text-color);
        }

        .toc {
            background: var(--card-background);
            padding: 2rem;
            border-radius: 8px;
            margin: 2rem 0;
        }

        .toc-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .toc ul {
            list-style: none;
        }

        .toc li {
            margin: 0.5rem 0;
        }

        .toc a {
            color: var(--text-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .toc a:hover {
            color: var(--primary-color);
        }

        .feature-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .card {
            background: var(--card-background);
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .callout {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .tip {
            background: var(--tip-background);
            border-left: 4px solid var(--primary-color);
        }

        .warning {
            background: var(--warning-background);
            border-left: 4px solid #f59e0b;
        }

        .note {
            background: var(--note-background);
            border-left: 4px solid #8b5cf6;
        }

        code {
            background: #f1f5f9;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: monospace;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        th, td {
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            text-align: left;
        }

        th {
            background: var(--card-background);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            h1 {
                font-size: 2rem;
            }

            .feature-cards {
                grid-template-columns: 1fr;
            }
        }

        .footer {
            margin-top: 4rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
            font-size: 0.875rem;
            color: #64748b;
        }


    ul {
        padding-left: 1.5rem;
        margin: 1rem 0;
    }

    ul li {
        position: relative;
        margin: 0.5rem 0;
        padding-left: 0.5rem;
    }

    .callout ul {
        margin: 0.5rem 0;
    }

    .card ul {
        list-style-type: none;
        padding-left: 0;
    }

    .card ul li {
        padding-left: 1.5rem;
        position: relative;
    }

    .card ul li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: var(--primary-color);
    }

    .toc ul {
        padding-left: 0;
    }

    .toc ul li {
        padding-left: 0;
    }

    .feature-cards .card ul {
        margin-top: 0.5rem;
    }
</style>
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 Complete Guide to Unity Vehicle Physics Implementation</h1>

        <div class="toc">
            <h2 class="toc-title">📑 Table of Contents</h2>
            <ul>
                <li><a href="#introduction"><i class="fas fa-book"></i> Introduction</a></li>
                <li><a href="#vehicle-controller"><i class="fas fa-car"></i> Vehicle Controller Setup</a></li>
                <li><a href="#camera-system"><i class="fas fa-camera"></i> Camera System</a></li>
                <li><a href="#lighting-system"><i class="fas fa-lightbulb"></i> Vehicle Lighting System</a></li>
                <li><a href="#input-system"><i class="fas fa-gamepad"></i> Input System</a></li>
                <li><a href="#additional-features"><i class="fas fa-plus-circle"></i> Additional Features</a></li>
                <li><a href="#optimization"><i class="fas fa-tachometer-alt"></i> Optimization Tips</a></li>
            </ul>
        </div>

        <div class="feature-cards">
            <div class="card">
                <i class="fas fa-car card-icon"></i>
                <h3>Realistic Physics</h3>
                <p>Advanced vehicle physics with wheel colliders and realistic suspension system</p>
            </div>
            <div class="card">
                <i class="fas fa-camera card-icon"></i>
                <h3>Multiple Cameras</h3>
                <p>Switchable camera views including orbit, follow, and dashboard cameras</p>
            </div>
            <div class="card">
                <i class="fas fa-lightbulb card-icon"></i>
                <h3>Dynamic Lighting</h3>
                <p>Complete lighting system with headlights, brake lights, and turn signals</p>
            </div>
            <div class="card">
                <i class="fas fa-mobile-alt card-icon"></i>
                <h3>Mobile Support</h3>
                <p>Cross-platform input system supporting both desktop and mobile controls</p>
            </div>
        </div>

        <!-- Content sections will continue here -->

        <section id="introduction">
            <h2><i class="fas fa-book"></i> Introduction</h2>
            <p>This comprehensive guide will walk you through implementing a complete vehicle physics system in Unity. The system includes realistic physics, multiple camera views, dynamic lighting, and mobile support.</p>
            
            <div class="callout tip">
                <h3>🎯 Quick Start Tip</h3>
                <p>Before starting, ensure you have Unity 2020.3 or later installed and basic knowledge of the Unity editor.</p>
            </div>
        </section>

       <!-- ... existing code until introduction section ... -->

        <section id="vehicle-controller">
            <h2><i class="fas fa-car"></i> Vehicle Controller Setup</h2>
            <p>The vehicle controller is the core component that handles physics, movement, and vehicle behavior. Let's break down how to set it up properly.</p>

            <h3>Basic Setup</h3>
            <div class="callout note">
                <h4>📝 Required Components</h4>
                <ul>
                    <li>Rigidbody component</li>
                    <li>4 Wheel Colliders</li>
                    <li>Vehicle mesh with wheel meshes</li>
                </ul>
            </div>

            <h3>Controller Configuration</h3>
            <table>
                <tr>
                    <th>Parameter</th>
                    <th>Recommended Value</th>
                    <th>Description</th>
                </tr>
                <tr>
                    <td>Motor Force</td>
                    <td>50f</td>
                    <td>Base force applied to wheels</td>
                </tr>
                <tr>
                    <td>Max Steer Angle</td>
                    <td>30f</td>
                    <td>Maximum wheel turning angle</td>
                </tr>
                <tr>
                    <td>Brake Force</td>
                    <td>500f</td>
                    <td>Force applied when braking</td>
                </tr>
            </table>

            <div class="callout tip">
                <h4>💡 Pro Tip</h4>
                <p>Adjust the Center of Mass using the centerOfMassObject to improve vehicle stability. Lower values generally provide better handling.</p>
            </div>
        </section>

        <section id="camera-system">
            <h2><i class="fas fa-camera"></i> Camera System</h2>
            <p>The system includes three camera types: Orbit, Follow, and Dashboard cameras.</p>

            <div class="feature-cards">
                <div class="card">
                    <i class="fas fa-sync card-icon"></i>
                    <h3>Orbit Camera</h3>
                    <p>Rotates around the vehicle with customizable distance and height</p>
                </div>
                <div class="card">
                    <i class="fas fa-running card-icon"></i>
                    <h3>Follow Camera</h3>
                    <p>Smoothly follows the vehicle from behind</p>
                </div>
                <div class="card">
                    <i class="fas fa-tachometer-alt card-icon"></i>
                    <h3>Dashboard Camera</h3>
                    <p>First-person view from inside the vehicle</p>
                </div>
            </div>

            <div class="callout warning">
                <h4>⚠️ Important Setup Note</h4>
                <p>Ensure proper camera target points are set up in your vehicle prefab:</p>
                <ul>
                    <li>Create empty GameObjects named "Target" for orbit camera</li>
                    <li>Create "Player" object for dashboard camera positioning</li>
                </ul>
            </div>
        </section>

        <section id="lighting-system">
            <h2><i class="fas fa-lightbulb"></i> Vehicle Lighting System</h2>
            <p>The lighting system provides realistic vehicle lights with multiple functions.</p>

            <h3>Light Types</h3>
            <ul>
                <li>Headlights (Toggle with 'H' key)</li>
                <li>Brake lights (Automatic)</li>
                <li>Turn signals (Toggle with 'T' key)</li>
                <li>Reverse lights (Automatic)</li>
                <li>Extra lights (Toggle with 'E' key)</li>
            </ul>

            <div class="callout tip">
                <h4>🔧 Setup Tips</h4>
                <p>For best results:</p>
                <ul>
                    <li>Use spot lights for headlights with 30-45 degree angles</li>
                    <li>Add light cookies for realistic headlight patterns</li>
                    <li>Use emission materials for light housings</li>
                </ul>
            </div>
        </section>

        <section id="input-system">
            <h2><i class="fas fa-gamepad"></i> Input System</h2>
            <p>The system supports both keyboard/mouse and mobile touch controls.</p>

            <table>
                <tr>
                    <th>Action</th>
                    <th>Keyboard</th>
                    <th>Mobile</th>
                </tr>
                <tr>
                    <td>Acceleration</td>
                    <td>W / Up Arrow</td>
                    <td>Accelerate Button</td>
                </tr>
                <tr>
                    <td>Brake/Reverse</td>
                    <td>S / Down Arrow</td>
                    <td>Brake Button</td>
                </tr>
                <tr>
                    <td>Steering</td>
                    <td>A,D / Left,Right</td>
                    <td>Steering Buttons</td>
                </tr>
            </table>
        </section>

        <section id="additional-features">
            <h2><i class="fas fa-plus-circle"></i> Additional Features</h2>
            
            <div class="feature-cards">
                <div class="card">
                    <i class="fas fa-camera-retro card-icon"></i>
                    <h3>Screenshot System</h3>
                    <p>Capture in-game screenshots with F1 key</p>
                </div>
                <div class="card">
                    <i class="fas fa-door-open card-icon"></i>
                    <h3>Door Mechanics</h3>
                    <p>Interactive door opening/closing system</p>
                </div>
                <div class="card">
                    <i class="fas fa-volume-up card-icon"></i>
                    <h3>Sound System</h3>
                    <p>Dynamic engine sounds and effects</p>
                </div>
            </div>
        </section>

        <section id="optimization">
            <h2><i class="fas fa-tachometer-alt"></i> Optimization Tips</h2>
            
            <div class="callout warning">
                <h4>🎯 Performance Recommendations</h4>
                <ul>
                    <li>Use LODs for vehicle models</li>
                    <li>Optimize light counts and shadows</li>
                    <li>Implement object pooling for particle effects</li>
                    <li>Use physics layers to optimize collisions</li>
                </ul>
            </div>

            <div class="callout note">
                <h4>📱 Mobile Optimization</h4>
                <p>For mobile platforms:</p>
                <ul>
                    <li>Reduce shadow resolution</li>
                    <li>Limit particle effects</li>
                    <li>Use simplified collision meshes</li>
                    <li>Implement quality settings manager</li>
                </ul>
            </div>
        </section>

              <section id="script-details">
            <h2><i class="fas fa-code"></i> Detailed Script Documentation</h2>
            
            <div class="callout note">
                <h4>📝 Script Organization</h4>
                <p>All scripts are prefixed with "Jrs" for easy identification and organization in the project.</p>
            </div>

            <h3><i class="fas fa-car"></i> Vehicle Core Scripts</h3>
            
            <div class="card">
                <h4>JrsVehicleController.cs</h4>
                <p>The main vehicle physics controller that handles:</p>
                <ul>
                    <li>Wheel physics and suspension</li>
                    <li>Engine power and gear system</li>
                    <li>Vehicle movement and steering</li>
                    <li>Sound effects and particle systems</li>
                </ul>
                <div class="callout tip">
                    <h5>Key Settings</h5>
                    <table>
                        <tr>
                            <th>Parameter</th>
                            <th>Default</th>
                            <th>Purpose</th>
                        </tr>
                        <tr>
                            <td>motorForce</td>
                            <td>50f</td>
                            <td>Engine power</td>
                        </tr>
                        <tr>
                            <td>maxSteerAngle</td>
                            <td>30f</td>
                            <td>Maximum turning angle</td>
                        </tr>
                        <tr>
                            <td>brakeForce</td>
                            <td>500f</td>
                            <td>Braking power</td>
                        </tr>
                    </table>
                </div>
            </div>

            <h3><i class="fas fa-camera"></i> Camera System Scripts</h3>
            
            <div class="feature-cards">
                <div class="card">
                    <h4>JrsCameraManager.cs</h4>
                    <p>Manages multiple camera views and handles switching between them:</p>
                    <ul>
                        <li>Camera array management</li>
                        <li>Smooth transitions</li>
                        <li>Key-based camera switching</li>
                        <li>Vehicle-specific camera setup</li>
                    </ul>
                </div>

                <div class="card">
                    <h4>JrsDashCamera.cs</h4>
                    <p>First-person dashboard view implementation:</p>
                    <ul>
                        <li>Offset: (0, 1.5, -5)</li>
                        <li>Smooth position following</li>
                        <li>View angle matching</li>
                        <li>Configurable sensitivity</li>
                    </ul>
                </div>

                <div class="card">
                    <h4>JrsFollowCamera.cs</h4>
                    <p>Third-person following camera with physics:</p>
                    <ul>
                        <li>Spring-based movement</li>
                        <li>Damping system</li>
                        <li>Height adjustment</li>
                        <li>Smooth target tracking</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-gamepad"></i> Input System Scripts</h3>
            
            <div class="card">
                <h4>JrsInputController.cs</h4>
                <p>Unified input system for both desktop and mobile:</p>
                <ul>
                    <li>Keyboard controls (WASD)</li>
                    <li>Mobile touch buttons</li>
                    <li>Camera controls</li>
                    <li>Vehicle function controls</li>
                </ul>
                <div class="callout tip">
                    <h5>Mobile Controls Setup</h5>
                    <p>Required buttons:</p>
                    <ul>
                        <li>Accelerate/Brake</li>
                        <li>Left/Right steering</li>
                        <li>Lights and special functions</li>
                        <li>Camera switching</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-door-open"></i> Vehicle Features Scripts</h3>
            
            <div class="feature-cards">
                <div class="card">
                    <h4>JrsDoorMechanic.cs</h4>
                    <p>Interactive door system:</p>
                    <ul>
                        <li>Smooth animation</li>
                        <li>Sound effects</li>
                        <li>Configurable rotation</li>
                        <li>Multiple axis support</li>
                    </ul>
                </div>

                <div class="card">
                    <h4>JrsVehicleLightControl.cs</h4>
                    <p>Complete vehicle lighting system:</p>
                    <ul>
                        <li>Headlights</li>
                        <li>Brake lights</li>
                        <li>Turn signals</li>
                        <li>Emergency lights</li>
                    </ul>
                </div>

                <div class="card">
                    <h4>JrsSteeringWheelAnimator.cs</h4>
                    <p>Realistic steering wheel movement:</p>
                    <ul>
                        <li>Input-based rotation</li>
                        <li>Smooth animation</li>
                        <li>Configurable angles</li>
                        <li>Speed-based response</li>
                    </ul>
                </div>
            </div>

            <h3><i class="fas fa-cog"></i> Utility Scripts</h3>
            
            <div class="feature-cards">
                <div class="card">
                    <h4>JrsCustomButton.cs</h4>
                    <p>Enhanced UI button system:</p>
                    <ul>
                        <li>Press/Release detection</li>
                        <li>Click events</li>
                        <li>State management</li>
                        <li>Mobile optimization</li>
                    </ul>
                </div>

                <div class="card">
                    <h4>JrsScreenshotManager.cs</h4>
                    <p>In-game screenshot functionality:</p>
                    <ul>
                        <li>Custom save location</li>
                        <li>Auto-incrementing names</li>
                        <li>High-resolution capture</li>
                        <li>Configurable hotkey</li>
                    </ul>
                </div>

                <div class="card">
                    <h4>JrsExitApplication.cs</h4>
                    <p>Application control utilities:</p>
                    <ul>
                        <li>Fullscreen toggle (F11)</li>
                        <li>Window resizing (F10)</li>
                        <li>Safe exit handling</li>
                        <li>Resolution management</li>
                    </ul>
                </div>
            </div>

            <div class="callout warning">
                <h4>⚠️ Implementation Notes</h4>
                <ul>
                    <li>Ensure all scripts are attached to appropriate GameObjects</li>
                    <li>Configure references in the Unity Inspector</li>
                    <li>Test all features in both play mode and builds</li>
                    <li>Verify mobile input when targeting mobile platforms</li>
                </ul>
            </div>
        </section>

        <!-- Continue with the footer section -->  <!-- ... existing footer section ... --> <!-- Additional sections will continue... -->

        <div class="footer">
            <h3>Attribution & Licenses</h3>
            <p>This guide uses the following open-source resources:</p>
            <ul>
                <li>Font: Inter (Google Fonts) - Licensed under the Open Font License</li>
                <li>Icons: Font Awesome Free - Licensed under CC BY 4.0</li>
            </ul>
            <p>© 2024 Your Name. Content available under MIT License.</p>
        </div>
    </div>
</body>
</html>