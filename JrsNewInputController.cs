using UnityEngine;
using UnityEngine.InputSystem;

public class JrsNewInputController : MonoBehaviour
{
    [Header("Input System Settings")]
    public VehicleInputActions vehicleInputActions;
    
    [Header("Mobile Touch Controls (Optional - for hybrid support)")]
    public JrsCustomButton accelerateButton;
    public JrsCustomButton revButton;
    public JrsCustomButton leftButton;
    public JrsCustomButton rightButton;
    public JrsCustomButton brakeButton;
    public JrsCustomButton headLightsButton;
    public JrsCustomButton sirenButton;
    public JrsCustomButton signalLightsButton;
    public JrsCustomButton extraLightsButton;
    public JrsCustomButton cameraButton;
    public JrsCustomButton doorToggleButton;

    [Header("Multi-Door Support (Optional - for vehicles with multiple doors)")]
    public JrsCustomButton door1Button;
    public JrsCustomButton door2Button;
    public JrsCustomButton door3Button;
    public JrsCustomButton door4Button;
    public JrsCustomButton door5Button;
    public JrsCustomButton door6Button;
    
    [Header("Camera Manager")]
    public JrsCameraManager cameraManager;
    
    [Header("Settings")]
    public float steerSpeed = 2f;
    public bool enableMobileControls = true; // Toggle for mobile control support
    
    // Input values
    private float verticalInput;
    private float horizontalInput;
    private bool isBraking;
    private bool wasCameraButtonPressed = false;
    
    // Input action references
    private InputAction accelerateAction;
    private InputAction reverseAction;
    private InputAction steerAction;
    private InputAction handBrakeAction;
    private InputAction headLightsAction;
    private InputAction sirenAction;
    private InputAction signalLightsAction;
    private InputAction extraLightsAction;
    private InputAction switchCameraAction;
    private InputAction doorToggleAction;
    private InputAction door1ToggleAction;
    private InputAction door2ToggleAction;
    private InputAction door3ToggleAction;
    private InputAction door4ToggleAction;
    private InputAction door5ToggleAction;
    private InputAction door6ToggleAction;
    
    // Light control states
    private bool headLightsPressed = false;
    private bool sirenPressed = false;
    private bool signalLightsPressed = false;
    private bool extraLightsPressed = false;

    // Door control state
    private bool doorTogglePressed = false;
    
    private void Awake()
    {
        // Initialize the input actions
        vehicleInputActions = new VehicleInputActions();
        
        // Get references to individual actions
        accelerateAction = vehicleInputActions.Vehicle.Accelerate;
        reverseAction = vehicleInputActions.Vehicle.Reverse;
        steerAction = vehicleInputActions.Vehicle.Steer;
        handBrakeAction = vehicleInputActions.Vehicle.HandBrake;
        headLightsAction = vehicleInputActions.Vehicle.HeadLights;
        sirenAction = vehicleInputActions.Vehicle.Siren;
        signalLightsAction = vehicleInputActions.Vehicle.SignalLights;
        extraLightsAction = vehicleInputActions.Vehicle.ExtraLights;
        switchCameraAction = vehicleInputActions.Vehicle.SwitchCamera;
        doorToggleAction = vehicleInputActions.Vehicle.DoorToggle;

        // Get individual door actions
        door1ToggleAction = vehicleInputActions.Vehicle.Door1Toggle;
        door2ToggleAction = vehicleInputActions.Vehicle.Door2Toggle;
        door3ToggleAction = vehicleInputActions.Vehicle.Door3Toggle;
        door4ToggleAction = vehicleInputActions.Vehicle.Door4Toggle;
        door5ToggleAction = vehicleInputActions.Vehicle.Door5Toggle;
        door6ToggleAction = vehicleInputActions.Vehicle.Door6Toggle;
    }
    
    private void OnEnable()
    {
        // Enable the input actions
        vehicleInputActions.Enable();
        
        // Subscribe to button press events for lights and camera
        headLightsAction.performed += OnHeadLightsPressed;
        sirenAction.performed += OnSirenPressed;
        signalLightsAction.performed += OnSignalLightsPressed;
        extraLightsAction.performed += OnExtraLightsPressed;
        switchCameraAction.performed += OnSwitchCameraPressed;
        doorToggleAction.performed += OnDoorTogglePressed;
    }
    
    private void OnDisable()
    {
        // Unsubscribe from events
        headLightsAction.performed -= OnHeadLightsPressed;
        sirenAction.performed -= OnSirenPressed;
        signalLightsAction.performed -= OnSignalLightsPressed;
        extraLightsAction.performed -= OnExtraLightsPressed;
        switchCameraAction.performed -= OnSwitchCameraPressed;
        doorToggleAction.performed -= OnDoorTogglePressed;
        
        // Disable the input actions
        vehicleInputActions.Disable();
    }
    
    private void Update()
    {
        HandleMovementInput();
        HandleMobileCameraInput();
    }
    
    private void HandleMovementInput()
    {
        // Reset input values
        verticalInput = 0f;
        
        // Handle acceleration and reverse from New Input System
        if (accelerateAction.IsPressed())
        {
            verticalInput = 1f;
        }
        else if (reverseAction.IsPressed())
        {
            verticalInput = -1f;
        }
        
        // Add mobile touch input support if enabled
        if (enableMobileControls)
        {
            if (accelerateButton != null && accelerateButton.IsButtonPressed())
            {
                verticalInput = 1f;
            }
            else if (revButton != null && revButton.IsButtonPressed())
            {
                verticalInput = -1f;
            }
        }
        
        // Handle steering from New Input System
        float targetHorizontalInput = steerAction.ReadValue<float>();
        
        // Add mobile touch steering if enabled
        if (enableMobileControls)
        {
            if (leftButton != null && leftButton.IsButtonPressed())
            {
                targetHorizontalInput = -1f;
            }
            else if (rightButton != null && rightButton.IsButtonPressed())
            {
                targetHorizontalInput = 1f;
            }
        }
        
        // Gradually change the horizontalInput value towards the targetHorizontalInput
        horizontalInput = Mathf.MoveTowards(horizontalInput, targetHorizontalInput, steerSpeed * Time.deltaTime);
        
        // Handle brake input
        isBraking = handBrakeAction.IsPressed();
        
        // Add mobile brake input if enabled
        if (enableMobileControls && brakeButton != null && brakeButton.IsButtonPressed())
        {
            isBraking = true;
        }
    }
    
    private void HandleMobileCameraInput()
    {
        // Handle mobile camera switching (since New Input System camera is handled via events)
        if (enableMobileControls && cameraButton != null)
        {
            bool isCameraButtonPressed = cameraButton.IsButtonPressed();
            if (isCameraButtonPressed && !wasCameraButtonPressed)
            {
                SwitchCamera();
            }
            wasCameraButtonPressed = isCameraButtonPressed;
        }
    }
    
    // Event handlers for button presses
    private void OnHeadLightsPressed(InputAction.CallbackContext context)
    {
        // Toggle headlights logic can be implemented here
        // For now, we'll just set a flag that can be read by other scripts
        headLightsPressed = !headLightsPressed;
        Debug.Log("HeadLights toggled: " + headLightsPressed);
    }
    
    private void OnSirenPressed(InputAction.CallbackContext context)
    {
        sirenPressed = !sirenPressed;
        Debug.Log("Siren toggled: " + sirenPressed);
    }
    
    private void OnSignalLightsPressed(InputAction.CallbackContext context)
    {
        signalLightsPressed = !signalLightsPressed;
        Debug.Log("Signal Lights toggled: " + signalLightsPressed);
    }
    
    private void OnExtraLightsPressed(InputAction.CallbackContext context)
    {
        extraLightsPressed = !extraLightsPressed;
        Debug.Log("Extra Lights toggled: " + extraLightsPressed);
    }
    
    private void OnSwitchCameraPressed(InputAction.CallbackContext context)
    {
        SwitchCamera();
    }

    private void OnDoorTogglePressed(InputAction.CallbackContext context)
    {
        doorTogglePressed = !doorTogglePressed;
        Debug.Log("Door toggle pressed");
    }
    
    private void SwitchCamera()
    {
        if (cameraManager != null && cameraManager.cameras.Length > 0)
        {
            cameraManager.SwitchToNextCamera();
        }
    }
    
    // Public methods to get input values (same interface as original JrsInputController)
    public float GetVerticalInput()
    {
        return verticalInput;
    }
    
    public float GetHorizontalInput()
    {
        return horizontalInput;
    }
    
    public bool IsBraking()
    {
        return isBraking;
    }
    
    // Public methods to get light states (for compatibility)
    public bool IsHeadLightsPressed()
    {
        return headLightsPressed;
    }

    public bool IsSirenPressed()
    {
        return sirenPressed;
    }

    public bool IsSignalLightsPressed()
    {
        return signalLightsPressed;
    }

    public bool IsExtraLightsPressed()
    {
        return extraLightsPressed;
    }

    // New methods for light input detection (returns true on button press, not state)
    public bool GetHeadLightsInput()
    {
        bool keyboardInput = headLightsAction.WasPressedThisFrame();
        bool mobileInput = enableMobileControls && headLightsButton != null && headLightsButton.IsButtonClicked();
        return keyboardInput || mobileInput;
    }

    public bool GetSignalLightsInput()
    {
        bool keyboardInput = signalLightsAction.WasPressedThisFrame();
        bool mobileInput = enableMobileControls && signalLightsButton != null && signalLightsButton.IsButtonClicked();
        return keyboardInput || mobileInput;
    }

    public bool GetExtraLightsInput()
    {
        bool keyboardInput = extraLightsAction.WasPressedThisFrame();
        bool mobileInput = enableMobileControls && extraLightsButton != null && extraLightsButton.IsButtonClicked();
        return keyboardInput || mobileInput;
    }

    public bool GetSirenInput()
    {
        bool keyboardInput = sirenAction.WasPressedThisFrame();
        bool mobileInput = enableMobileControls && sirenButton != null && sirenButton.IsButtonClicked();
        return keyboardInput || mobileInput;
    }

    // Public method to get door toggle input (for door mechanic) - Generic door toggle
    public bool GetDoorToggleInput()
    {
        // Check keyboard input
        bool keyboardInput = doorToggleAction.WasPressedThisFrame();

        // Check mobile button input if enabled
        bool mobileInput = false;
        if (enableMobileControls && doorToggleButton != null)
        {
            mobileInput = doorToggleButton.IsButtonClicked();
        }

        return keyboardInput || mobileInput;
    }

    // Public method to get specific door toggle input (for multi-door vehicles)
    public bool GetDoorToggleInput(string doorID)
    {
        bool keyboardInput = false;
        bool mobileInput = false;

        // Check keyboard input based on door ID
        switch (doorID.ToLower())
        {
            case "door1":
                keyboardInput = door1ToggleAction?.WasPressedThisFrame() ?? false;
                if (enableMobileControls && door1Button != null)
                    mobileInput = door1Button.IsButtonClicked();
                break;
            case "door2":
                keyboardInput = door2ToggleAction?.WasPressedThisFrame() ?? false;
                if (enableMobileControls && door2Button != null)
                    mobileInput = door2Button.IsButtonClicked();
                break;
            case "door3":
                keyboardInput = door3ToggleAction?.WasPressedThisFrame() ?? false;
                if (enableMobileControls && door3Button != null)
                    mobileInput = door3Button.IsButtonClicked();
                break;
            case "door4":
                keyboardInput = door4ToggleAction?.WasPressedThisFrame() ?? false;
                if (enableMobileControls && door4Button != null)
                    mobileInput = door4Button.IsButtonClicked();
                break;
            case "door5":
                keyboardInput = door5ToggleAction?.WasPressedThisFrame() ?? false;
                if (enableMobileControls && door5Button != null)
                    mobileInput = door5Button.IsButtonClicked();
                break;
            case "door6":
                keyboardInput = door6ToggleAction?.WasPressedThisFrame() ?? false;
                if (enableMobileControls && door6Button != null)
                    mobileInput = door6Button.IsButtonClicked();
                break;
            default:
                // Fallback to generic door toggle
                return GetDoorToggleInput();
        }

        return keyboardInput || mobileInput;
    }

    // Method to enable/disable mobile controls at runtime
    public void SetMobileControlsEnabled(bool enabled)
    {
        enableMobileControls = enabled;
    }
}
