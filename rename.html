<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jrs Object Renamer Tool for Unity Beginners</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        @import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap");
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            color: #333;
            line-height: 1.6;
        }

        header {
            background-color: #4CAF50;
            color: #fff;
            text-align: center;
            padding: 2em 0;
        }

        header h1 {
            margin: 0;
            font-size: 2.5rem;
        }

        header p {
            margin: 0.5em 0 0;
            font-size: 1.2rem;
        }

        main {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        section {
            margin-bottom: 40px;
        }

        h2 {
            color: #4CAF50;
            font-size: 2rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }

        ol, ul {
            list-style: none;
            padding: 0;
        }

        ol li {
            margin-bottom: 15px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
        }

        ol li::before {
            content: "➤";
            color: #4CAF50;
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .toc-section {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .toc-section i {
            margin-right: 8px;
            color: #4CAF50;
        }

        .toc-section a {
            text-decoration: none;
            color: #333;
            font-size: 1.1rem;
        }

        .toc-section a:hover {
            color: #4CAF50;
        }

        .interactive-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .card i {
            font-size: 2.5rem;
            color: #4CAF50;
            margin-bottom: 15px;
        }

        .card h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #333;
        }

        .card p {
            font-size: 1rem;
            color: #666;
        }

        footer {
            background-color: #333;
            color: #fff;
            text-align: center;
            padding: 1em 0;
            margin-top: 40px;
        }

        footer p {
            margin: 0;
        }
    </style>
</head>
<body>
    <header>
        <h1>Jrs Object Renamer Tool for Unity Beginners</h1>
        <p>Simplify your scene organization with this powerful tool!</p>
    </header>

    <main>
        <section id="table-of-contents">
            <h2>Table of Contents</h2>
            <div class="toc-section">
                <i class="iconfont">&#x1F4D6;</i> <a href="#introduction">Introduction</a>
            </div>
            <div class="toc-section">
                <i class="iconfont">&#x1F4A1;</i> <a href="#why-use-it">Why Use It?</a>
            </div>
            <div class="toc-section">
                <i class="iconfont">&#x1F4CB;</i> <a href="#step-by-step-guide">Step-by-Step Guide</a>
            </div>
            <div class="toc-section">
                <i class="iconfont">&#x1F4BB;</i> <a href="#features">Features</a>
            </div>
            <div class="toc-section">
                <i class="iconfont">&#x1F4DD;</i> <a href="#tips-and-tricks">Tips and Tricks</a>
            </div>
        </section>

        <section id="introduction">
            <h2>Introduction</h2>
            <p>The Jrs Object Renamer Tool is a custom editor window for Unity that allows you to quickly and efficiently rename multiple GameObjects within your scene. It’s a fantastic tool for organizing your projects, especially when you have a large number of objects.</p>
        </section>

        <section id="why-use-it">
            <h2>Why Use It?</h2>
            <p><b>Time-Saving:</b> Manually renaming hundreds of objects is incredibly tedious. This tool automates the process, saving you hours of work. <i>(Tip: Organization is key to efficient game development!)</i></p>
            <p><b>Error Reduction:</b> Manual renaming is prone to errors. This tool minimizes the risk of typos and inconsistencies.</p>
            <p><b>Improved Workflow:</b> Streamline your scene management and improve your overall development workflow.</p>
        </section>

        <section id="step-by-step-guide">
            <h2>Step-by-Step Guide</h2>
            <ol>
                <li>Open your Unity project.</li>
                <li>Locate the "Jrs Object Renamer Tool" in the "Tools" menu.</li>
                <li>The tool will open.</li>
                <li>In the "Search Name" field, enter the name (or part of the name) of the GameObjects you want to rename.</li>
                <li>Choose the search type: "Exact Match Only" or "Contains".</li>
                <li>Click the "Search Objects" button.</li>
                <li>The tool will display a list of matching GameObjects.</li>
                <li>In the "New Name" field, enter the new name for the selected GameObjects.</li>
                <li>Click the "Rename All" button.</li>
                <li>The tool will rename all matching GameObjects to the new name.</li>
            </ol>
        </section>

        <section id="features">
            <h2>Features</h2>
            <div class="interactive-cards">
                <div class="card">
                    <i class="iconfont">&#x1F4C2;</i>
                    <h3>Bulk Renaming</h3>
                    <p>Rename multiple objects at once.</p>
                </div>
                <div class="card">
                    <i class="iconfont">&#x1F50D;</i>
                    <h3>Flexible Search</h3>
                    <p>Search by exact name or partial name.</p>
                </div>
                <div class="card">
                    <i class="iconfont">&#x21A9;</i>
                    <h3>Undo/Redo</h3>
                    <p>Easily undo or redo your changes.</p>
                </div>
                <div class="card">
                    <i class="iconfont">&#x1F4DD;</i>
                    <h3>User-Friendly Interface</h3>
                    <p>Simple and intuitive design for easy use.</p>
                </div>
            </div>
        </section>

        <section id="tips-and-tricks">
            <h2>Tips and Tricks</h2>
            <ul>
                <li><b>Use Descriptive Names:</b> Choose names that clearly indicate the purpose of each GameObject.</li>
                <li><b>Regularly Rename:</b> As you develop your game, rename objects as needed to maintain a consistent naming scheme.</li>
                <li><b>Start Small:</b> When experimenting with the tool, start with a small number of objects to get a feel for how it works.</li>
            </ul>
        </section>
    </main>

    <footer>
        <p>Created with love for Unity developers!</p>
    </footer>
</body>
</html>