using UnityEditor;
using UnityEngine;
using System;
using System.Linq;
using System.Collections.Generic;
using UnityEditor.IMGUI.Controls;

public class CoroutineStarter : MonoBehaviour
{
    public System.Collections.IEnumerator coroutine;

    public void StartCoroutineWrapper()
    {
        StartCoroutine(coroutine);
    }
}

public class JrsVehicleSetupTool : EditorWindow
{
    private GameObject templateVehicle;
    private GameObject newVehicleModel;
    public GameObject wheelPrefab;

    private Vector2 scrollPosition;
    private Vector2 namingConventionScrollPos;
    private Vector2 logReportScrollPosition;
    private List<string> logMessages = new List<string>();
    private Dictionary<string, Color> logColors = new Dictionary<string, Color>();
    private GUIStyle logStyle;

    public WheelCollider[] wheelCollidersBrake;

    [MenuItem("Tools/Jrs Vehicle Setup")]
    public static void ShowWindow()
    {
        GetWindow<JrsVehicleSetupTool>("Vehicle Setup");
    }

    private void OnGUI()
    {
        // Initialize styles
        if (logStyle == null)
        {
            logStyle = new GUIStyle(EditorStyles.label);
            logStyle.wordWrap = true;
        }

        // Create custom styles for colored text
        GUIStyle yellowStyle = new GUIStyle(EditorStyles.label) {
            normal = { textColor = Color.yellow },
            hover = { textColor = Color.yellow },
            active = { textColor = Color.yellow },
            wordWrap = true
        };
        
        GUIStyle greenStyle = new GUIStyle(EditorStyles.label) {
            normal = { textColor = new Color(0.56f, 0.93f, 0.56f) }, // Light green
            hover = { textColor = new Color(0.56f, 0.93f, 0.56f) },
            active = { textColor = new Color(0.56f, 0.93f, 0.56f) }
        };
        
        GUIStyle blueStyle = new GUIStyle(EditorStyles.label) {
            normal = { textColor = new Color(0.68f, 0.85f, 0.9f) }, // Light blue
            hover = { textColor = new Color(0.68f, 0.85f, 0.9f) },
            active = { textColor = new Color(0.68f, 0.85f, 0.9f) }
        };

        // Background box
        Rect backgroundRect = new Rect(10, 10, position.width - 20, position.height - 20);
        GUI.Box(backgroundRect, "");

        // Main scroll view for entire window
        scrollPosition = GUILayout.BeginScrollView(scrollPosition);
        GUILayout.BeginArea(new Rect(20, 20, position.width - 40, position.height - 40));
        GUILayout.Label("JRS Vehicle Setup Tool", EditorStyles.boldLabel);

        // Vehicle setup fields
        templateVehicle = (GameObject)EditorGUILayout.ObjectField("Template Vehicle", templateVehicle, typeof(GameObject), true);
        newVehicleModel = (GameObject)EditorGUILayout.ObjectField("New Vehicle Model", newVehicleModel, typeof(GameObject), true);
        wheelPrefab = (GameObject)EditorGUILayout.ObjectField("Wheel Prefab", wheelPrefab, typeof(GameObject), true);


        if (GUILayout.Button("Setup Vehicle"))
        {
            if (templateVehicle == null || newVehicleModel == null)
            {
                AddLog("Please assign both template vehicle and new vehicle model.", Color.red);
                return;
            }

            // Check if template vehicle is a prefab instance and unpack it if needed
            if (PrefabUtility.IsPartOfPrefabInstance(templateVehicle))
            {
                PrefabUtility.UnpackPrefabInstance(templateVehicle, PrefabUnpackMode.OutermostRoot, InteractionMode.UserAction);
            }

            logMessages.Clear();
            logColors.Clear();
            SetupNewVehicle();
        }

        // Naming convention instructions with scroll view
        GUILayout.Space(15);
        EditorGUILayout.LabelField("Naming Convention", EditorStyles.boldLabel);
        
        // Box around naming convention section
        EditorGUILayout.BeginVertical(GUI.skin.box);
        
        // Scrollable naming convention section
        namingConventionScrollPos = EditorGUILayout.BeginScrollView(namingConventionScrollPos, 
            GUILayout.Height(200), 
            GUILayout.MaxHeight(position.height - 400));
        
        // Yellow text for important notice
        GUILayout.Label("*Important please read: Before proceeding, please carefully check the naming convention, ensuring case sensitivity for each object in your vehicle. Follow the naming convention below if you have these objects in your vehicle:", yellowStyle);
        
        // Light green text for vehicle parts
        GUILayout.Label("\nVEHICLE");
        GUILayout.Label("brake_light\nfog_light\nheadlight\nreverse_light\nroof_light\nturn_signal\nbumper_back\nbumper_front\ndashboard\nDoorFrontLeft\nDoorFrontRight\nDoorRearLeft\nDoorRearRight\nunder_body\nsteering_wheel", greenStyle);
        
        // Light blue text for wheel hierarchy
        GUILayout.Label("\nWHEEL");
        GUILayout.Label("wheel_mesh", greenStyle);
        GUILayout.Label("(if your wheel have brakes: Create an empty game object \"main_wheel\" and then parent your \"wheel_mesh\" and \"brake_mesh\" inside \"main_wheel\")", yellowStyle);
        GUILayout.Label("main_wheel\n  ↳wheel_mesh\n  ↳brake_mesh", blueStyle);
        
        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
        GUILayout.Space(15);


       // Log report
        GUILayout.Space(10);
        GUILayout.Label("Log Report", EditorStyles.boldLabel);
        EditorGUILayout.BeginVertical(GUI.skin.box);
        logReportScrollPosition = EditorGUILayout.BeginScrollView(logReportScrollPosition, GUILayout.Height(100));
        foreach (string message in logMessages)
        {
            GUIStyle messageStyle = new GUIStyle(EditorStyles.label);
            messageStyle.wordWrap = true;
            if (logColors.ContainsKey(message))
            {
                messageStyle.normal.textColor = logColors[message];
                messageStyle.hover.textColor = logColors[message];
            }
            else
            {
                messageStyle.normal.textColor = Color.white;
                messageStyle.hover.textColor = Color.white;
            }
            GUILayout.Label(message, messageStyle);
        }
        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
        GUILayout.EndArea();
        GUILayout.EndScrollView();
    }

    private void AddLog(string message, Color color)
    {
        logMessages.Add(message);
        logColors[message] = color;
        Debug.Log(message);
    }

    private System.Collections.IEnumerator AnimateVehicleAppearance(GameObject vehicle)
    {
        // Initial delay before starting animation
        yield return new WaitForSeconds(0.5f);

        // Activate wheels first
        Transform[] wheels = vehicle.GetComponentsInChildren<Transform>()
            .Where(t => t.name.Contains("wheel_mesh") && !t.name.Contains("spare"))
            .ToArray();
            
        foreach (var wheel_mesh in wheels)
        {
            wheel_mesh.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.1f);
        }

        // Activate underbody
        Transform underbody = vehicle.transform.Find("under_body") ?? 
                            vehicle.transform.Find("underbody");
        if (underbody != null)
        {
            underbody.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }

        // Activate dashboard
        Transform dashboard = vehicle.transform.Find("dashboard") ?? 
                            vehicle.transform.Find("dash_board");
        if (dashboard != null)
        {
            dashboard.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }

        // Activate seatfront
        Transform seatfront = vehicle.transform.Find("seat_front") ?? 
                            vehicle.transform.Find("seatfront");
        if (seatfront != null)
        {
            seatfront.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }
        
        // Activate seatback
        Transform seatback = vehicle.transform.Find("seat_back") ?? 
                            vehicle.transform.Find("seatfback");
        if (seatback != null)
        {
            seatback.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }

        // Activate cargoarea
        Transform cargoarea = vehicle.transform.Find("cargo_area") ?? 
                            vehicle.transform.Find("cargoarea");
        if (cargoarea != null)
        {
            cargoarea.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }
        // Activate bumperfront
        Transform bumperfront = vehicle.transform.Find("bumper_front") ?? 
                            vehicle.transform.Find("bumperfront");
        if (bumperfront != null)
        {
            bumperfront.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }
        // Activate bumperback
        Transform bumperback = vehicle.transform.Find("bumper_back") ?? 
                            vehicle.transform.Find("bumperback");
        if (bumperback != null)
        {
            bumperback.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }
        // Activate steps
        Transform steps = vehicle.transform.Find("steps"); 
                        
        if (steps != null)
        {
            steps.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }
        // Activate DoorFrontLeft
        Transform DoorFrontLeft = vehicle.transform.Find("DoorFrontLeft"); 
                        
        if (DoorFrontLeft != null)
        {
            DoorFrontLeft.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }
        // Activate DoorFrontRight
        Transform DoorFrontRight = vehicle.transform.Find("DoorFrontRight"); 
                        
        if (DoorFrontRight != null)
        {
            DoorFrontRight.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }
        // Activate DoorRearLeft
        Transform DoorRearLeft = vehicle.transform.Find("DoorRearLeft"); 
                        
        if (DoorRearLeft != null)
        {
            DoorRearLeft.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }
        // Activate DoorRearRight
        Transform DoorRearRight = vehicle.transform.Find("DoorRearRight"); 
                        
        if (DoorRearRight != null)
        {
            DoorRearRight.gameObject.SetActive(true);
            yield return new WaitForSeconds(0.3f);
        }



        // Activate steering_wheel
        Transform steering = vehicle.transform.Find("steering");
        if (steering != null)
        {
            Transform steeringwheel = steering.Find("steering_wheel");
            if (steeringwheel != null)
            {
                steeringwheel.gameObject.SetActive(true);
                yield return new WaitForSeconds(0.3f);
            }
        }

        // Activate remaining parts
        foreach (Transform child in vehicle.transform)
        {
            if (!child.gameObject.activeSelf)
            {
                child.gameObject.SetActive(true);
                yield return new WaitForSeconds(0.05f);
            }
        }

        // Disable specific objects after animation
        string[] objectsToDisable = new string[] {
            "brake_light",
            "fog_light",
            "headlight",
            "reverse_light",
            "roof_light",
            "turn_signal",
            "headlight_source"
        };
        
        // Ask user if they want to add the vehicle to the vehicle list
        int option = EditorUtility.DisplayDialogComplex(
            "Add Vehicle to List?",
            "Would you like to add this vehicle to the Vehicle Selector, so you can select and drive it in the game?",
            "Yes",
            "Don't Add this Vehicle",
            "Cancel"
        );

        if (option == 0) // User clicked "Yes"
        {
            AddVehicleToSelector(vehicle);
        }
        else if (option == 1) // User clicked "Don't Add this Vehicle"
        {
            Debug.Log("Vehicle not added to vehicle list.");
        }
        else // User clicked "Cancel" or closed the dialog
        {
            Debug.Log("Vehicle addition cancelled.");
        }

        foreach (string objectName in objectsToDisable)
        {
            if (objectName.Equals("steering_wheel", StringComparison.OrdinalIgnoreCase)) continue;
            Transform obj = vehicle.transform.Find(objectName) ?? FindChildRecursive(vehicle.transform, objectName);
            if (obj != null)
            {
                obj.gameObject.SetActive(false);
            }
        }
    }

    private void SetupNewVehicle()
    {
        try
        {
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Initializing...", 0f);
            
            // Create new instance of the vehicle and hide all parts initially
            GameObject newVehicle = Instantiate(newVehicleModel);
            newVehicle.name = newVehicleModel.name;
            
            // Hide all parts initially
            foreach (Transform child in newVehicle.transform)
            {
                child.gameObject.SetActive(false);
            }

            // Copy the transform of the template vehicle to the new vehicle
            newVehicle.transform.position = templateVehicle.transform.position;
            newVehicle.transform.rotation = templateVehicle.transform.rotation;

            // Copy core components
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Copying core components...", 0.1f);
            CopyComponent<JrsVehicleController>(templateVehicle, newVehicle);
            CopyComponent<Rigidbody>(templateVehicle, newVehicle);
            
            // Copy and setup light control
            CopyComponent<JrsVehicleLightControl>(templateVehicle, newVehicle);
            AssignLightElements(newVehicle);

            // Setup wheel colliders and transforms
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Setting up wheels...", 0.3f);
            var templateController = templateVehicle.GetComponent<JrsVehicleController>();
            var newController = newVehicle.GetComponent<JrsVehicleController>();

            if (templateController != null && newController != null)
            {
                newController.wheels = new WheelCollider[templateController.wheels.Length];
                newController.wheelTransforms = new Transform[templateController.wheelTransforms.Length];
                newController.wheelCollidersBrake = new WheelCollider[templateController.wheelCollidersBrake.Length];

                // Create and assign wheel transforms
                for (int i = 0; i < templateController.wheelTransforms.Length; i++)
                {
                    Transform templateWheelTransform = templateController.wheelTransforms[i];

                    // Get the center of the template vehicle
                    Transform templateVehicleCenter = templateVehicle.transform.Find("CenterOfMass");
                    if (templateVehicleCenter == null)
                    {
                        Rigidbody templateRigidbody = templateVehicle.GetComponent<Rigidbody>();
                        if (templateRigidbody != null)
                        {
                            templateVehicleCenter = new GameObject("CenterOfMass").transform;
                            templateVehicleCenter.SetParent(templateVehicle.transform);
                            templateVehicleCenter.position = templateVehicle.transform.position + templateRigidbody.centerOfMass;
                        }
                        else
                        {
                            Debug.LogWarning("Could not find CenterOfMass or Rigidbody on template vehicle. Using vehicle origin.");
                            templateVehicleCenter = templateVehicle.transform;
                        }
                    }

                    // Get the center of the new vehicle
                    Transform newVehicleCenter = newVehicle.transform.Find("CenterOfMass");
                    if (newVehicleCenter == null)
                    {
                        Rigidbody newRigidbody = newVehicle.GetComponent<Rigidbody>();
                        if (newRigidbody != null)
                        {
                            newVehicleCenter = new GameObject("CenterOfMass").transform;
                            newVehicleCenter.SetParent(newVehicle.transform);
                            newVehicleCenter.position = newVehicle.transform.position + newRigidbody.centerOfMass;
                        }
                        else
                        {
                            Debug.LogWarning("Could not find CenterOfMass or Rigidbody on new vehicle. Using vehicle origin.");
                            newVehicleCenter = newVehicle.transform;
                        }
                    }

                    // Instantiate wheel prefab directly under vehicle
                    GameObject wheelInstance = (GameObject)UnityEditor.PrefabUtility.InstantiatePrefab(wheelPrefab);
                    wheelInstance.name = templateWheelTransform.name;
                    wheelInstance.transform.SetParent(newVehicle.transform);

                    // Calculate and set position relative to vehicle center
                    Vector3 relativePosition = templateWheelTransform.position - templateVehicleCenter.position;
                    Vector3 newWheelPosition = newVehicleCenter.position + relativePosition;
                    wheelInstance.transform.position = newWheelPosition;
                    wheelInstance.transform.localRotation = templateWheelTransform.localRotation;

                    // Get or add WheelCollider to the wheel instance
                    WheelCollider newWheelCollider = wheelInstance.GetComponent<WheelCollider>();
                    if (newWheelCollider == null)
                    {
                        newWheelCollider = wheelInstance.AddComponent<WheelCollider>();
                    }

                    // Copy WheelCollider settings
                    WheelCollider templateWheelCollider = templateController.wheels[i];
                    CopyWheelColliderSettings(templateWheelCollider, newWheelCollider);

                    // Assign the wheel collider and transform to the controller
                    newController.wheels[i] = newWheelCollider;
                    
                    // Find the "wheel_mesh" child transform
                    Transform wheelChild = wheelInstance.transform.Find("wheel_mesh");
                    if (wheelChild != null) {
                        newController.wheelTransforms[i] = wheelChild;
                    } else {
                        Debug.LogWarning($"Could not find 'wheel_mesh' child in {wheelInstance.name}, creating one from the parent's mesh.");

                        // Get the mesh from the wheelInstance
                        MeshFilter meshFilter = wheelInstance.GetComponent<MeshFilter>();
                        if (meshFilter == null || meshFilter.sharedMesh == null)
                        {
                            Debug.LogError($"Could not find MeshFilter or Mesh on {wheelInstance.name}");
                            newController.wheelTransforms[i] = wheelInstance.transform;
                        }
                        else
                        {
                            // Create a new GameObject for the wheel_mesh
                            GameObject wheelMeshObject = new GameObject("wheel_mesh");
                            wheelMeshObject.transform.SetParent(wheelInstance.transform);
                            wheelMeshObject.transform.localPosition = Vector3.zero;
                            wheelMeshObject.transform.localRotation = Quaternion.identity;

                            // Add a MeshFilter and MeshRenderer to the new object
                            MeshFilter newMeshFilter = wheelMeshObject.AddComponent<MeshFilter>();
                            newMeshFilter.sharedMesh = meshFilter.sharedMesh;
                            MeshRenderer newMeshRenderer = wheelMeshObject.AddComponent<MeshRenderer>();
                            newMeshRenderer.sharedMaterial = wheelInstance.GetComponent<MeshRenderer>().sharedMaterial; // Copy material from parent

                            // Remove MeshFilter and MeshRenderer from the wheelInstance
                            DestroyImmediate(wheelInstance.GetComponent<MeshFilter>());
                            DestroyImmediate(wheelInstance.GetComponent<MeshRenderer>());

                            newController.wheelTransforms[i] = wheelMeshObject.transform;
                        }
                    }

                    newController.wheelCollidersBrake[i] = newWheelCollider;

                    // Add Jrs Lock Rotation component to brake object
                    Transform newBrake = wheelInstance.transform.Find("brake");
                    if (newBrake != null)
                    {
                        // Add or get existing JrsLockRotation component
                        JrsLockRotation lockRotation = newBrake.GetComponent<JrsLockRotation>();
                        if (lockRotation == null)
                        {
                            lockRotation = newBrake.gameObject.AddComponent<JrsLockRotation>();
                        }
                        
                        // Assign the wheel_mesh transform to the lock rotation component
                        lockRotation.wheel_mesh = newController.wheelTransforms[i];
                        Debug.Log($"Added JrsLockRotation to {newBrake.name} and assigned wheel_mesh transform");
                    }
                    else
                    {
                        Debug.LogWarning($"Could not find brake object in {wheelInstance.name}");
                    }
                }

                // Assign dust trails to the controller with debug logging
                AssignDustTrail(newController, newVehicle, "dust_trail_FL", "frontLeftDustParticleSystem");
                AssignDustTrail(newController, newVehicle, "dust_trail_FR", "frontRightDustParticleSystem");
                AssignDustTrail(newController, newVehicle, "dust_trail_RL", "rearLeftDustParticleSystem");
                AssignDustTrail(newController, newVehicle, "dust_trail_RR", "rearRightDustParticleSystem");

                // Handle spare wheel if exists (visual only)
                Transform templateSpareWheel = FindChildRecursive(templateVehicle.transform, "wheel_spare");
                if (templateSpareWheel != null)
                {
                    // Get the center of the template vehicle
                    Transform templateVehicleCenter = templateVehicle.transform.Find("CenterOfMass");
                    if (templateVehicleCenter == null)
                    {
                        templateVehicleCenter = templateVehicle.transform;
                    }

                    // Get the center of the new vehicle
                    Transform newVehicleCenter = newVehicle.transform.Find("CenterOfMass");
                    if (newVehicleCenter == null)
                    {
                        newVehicleCenter = newVehicle.transform;
                    }

                    // Instantiate spare wheel prefab
                    GameObject spareWheelInstance = (GameObject)UnityEditor.PrefabUtility.InstantiatePrefab(wheelPrefab);
                    spareWheelInstance.name = "wheel_spare";
                    spareWheelInstance.transform.SetParent(newVehicle.transform);

                    // Calculate and set position relative to vehicle center
                    Vector3 relativePosition = templateSpareWheel.position - templateVehicleCenter.position;
                    Vector3 newWheelPosition = newVehicleCenter.position + relativePosition;
                    spareWheelInstance.transform.position = newWheelPosition;
                    spareWheelInstance.transform.localRotation = templateSpareWheel.localRotation;

                    // Hide the "brake" child object
                    Transform brake = spareWheelInstance.transform.Find("brake");
                    if (brake != null)
                    {
                        brake.gameObject.SetActive(false);
                        Debug.Log("Hidden 'brake' child from spare wheel");
                    }

                    Debug.Log("Successfully added visual spare wheel");
                }

                // Force serialization
                EditorUtility.SetDirty(newController);
                PrefabUtility.RecordPrefabInstancePropertyModifications(newController);
                Debug.Log("Dust trail assignments completed and serialized");
               
                // Assign CenterOfMass object
                Transform newCenterOfMass = newVehicle.transform.Find("CenterOfMass");
                if (newCenterOfMass != null)
                {
                    newController.centerOfMassObject = newCenterOfMass.gameObject;
                    EditorUtility.SetDirty(newController);
                    PrefabUtility.RecordPrefabInstancePropertyModifications(newController);
                }
                else
                {
                    Debug.LogWarning("Could not find CenterOfMass object in new vehicle");
                }
            }

            // Copy all BoxColliders recursively, except for doors which are handled separately
            BoxCollider[] templateColliders = templateVehicle.GetComponentsInChildren<BoxCollider>();
            foreach (BoxCollider templateCollider in templateColliders)
            {
                // Skip door colliders since they are handled in SetupDoors
                if (templateCollider.transform.name.Contains("Door")) continue;
                
                // Find corresponding transform in new vehicle
                string path = GetTransformPath(templateCollider.transform, templateVehicle.transform);
                Transform newTransform = newVehicle.transform.Find(path);
                
                if (newTransform != null)
                {
                    BoxCollider newCollider = newTransform.gameObject.AddComponent<BoxCollider>();
                    EditorUtility.CopySerialized(templateCollider, newCollider);
                    Debug.Log($"Copied BoxCollider from {templateCollider.transform.name} to {newTransform.name}");
                }
            }

            // Setup doors
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Setting up doors...", 0.55f);
            SetupDoors(templateVehicle, newVehicle);

            // Copy audio sources
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Setting up audio...", 0.6f);
            CopyAudioSources(templateVehicle, newVehicle);

            // Copy dust trail particle systems
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Setting up dust trails...", 0.8f);
            CopyDustTrails(templateVehicle, newVehicle);

            // Copy other particle systems
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Setting up effects...", 0.85f);
            CopyParticleSystems(templateVehicle, newVehicle);

            // Setup steering wheel
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Setting up steering...", 0.9f);
            SetupSteering(templateVehicle, newVehicle);

            // Copy additional game objects
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Copying additional objects...", 0.92f);
            CopyGameObjects(templateVehicle, newVehicle);

            // Assign light elements
            EditorUtility.DisplayProgressBar("Setting up vehicle", "Assigning light elements...", 0.95f);
            AssignLightElements(newVehicle);

            Debug.Log("Vehicle setup complete!");

            // Start appearance animation
            CoroutineStarter coroutineStarter = newVehicle.AddComponent<CoroutineStarter>();
            coroutineStarter.coroutine = AnimateVehicleAppearance(newVehicle);
            coroutineStarter.StartCoroutineWrapper();

            // Delete the template vehicle after successful setup
            if (templateVehicle != null)
            {
                Undo.DestroyObjectImmediate(templateVehicle);
                Debug.Log("Template vehicle deleted successfully");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error setting up vehicle: {e.Message}\nStack Trace:\n{e.StackTrace}");
            EditorUtility.DisplayDialog("Error", $"Error setting up vehicle: {e.Message}\n\nSee console for more details", "OK");
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }
    }


    private void AddVehicleToSelector(GameObject newVehicle)
    {
        JrsVehicleSelector vehicleSelector = FindObjectOfType<JrsVehicleSelector>();

        if (vehicleSelector == null)
        {
            Debug.LogError("JrsVehicleSelector not found in the scene.");
            return;
        }

        List<JrsVehicleController> vehicles = vehicleSelector.vehicles;
        if (vehicles == null)
        {
            Debug.LogError("vehicleSelector.vehicles is null");
            return;
        }

        int emptyIndex = -1;

        for (int i = 0; i < vehicles.Count; i++)
        {
            if (vehicles[i] == null)
            {
                emptyIndex = i;
                break;
            }
        }

        JrsVehicleController newVehicleController = newVehicle.GetComponent<JrsVehicleController>();
        if (newVehicleController == null)
        {
            Debug.LogError("JrsVehicleController not found on the new vehicle.");
            return;
        }

        if (emptyIndex != -1)
        {
            vehicles[emptyIndex] = newVehicleController;
            Debug.Log("Vehicle added to existing slot in vehicle list.");
        }
        else
        {
            vehicles.Add(newVehicleController);
            Debug.Log("Vehicle added to new slot in vehicle list.");
        }

        EditorUtility.SetDirty(vehicleSelector);
        PrefabUtility.RecordPrefabInstancePropertyModifications(vehicleSelector);
    }

    private void SetupDoors(GameObject template, GameObject newVehicle)
    {
        try
        {
            string[] doorNames = new string[] { "DoorFrontLeft", "DoorFrontRight", "DoorRearLeft", "DoorRearRight" };

            foreach (string doorName in doorNames)
            {
                // Find door in template
                Transform templateDoor = FindChildRecursive(template.transform, doorName);
                if (templateDoor == null)
                {
                    Debug.LogWarning($"Could not find template door: {doorName}");
                    continue;
                }

                // Find corresponding door in new vehicle
                Transform newDoor = FindChildRecursive(newVehicle.transform, doorName);
                if (newDoor == null)
                {
                    Debug.LogWarning($"Could not find new vehicle door: {doorName}");
                    continue;
                }

                // Add BoxCollider if it doesn't exist
                if (newDoor.GetComponent<BoxCollider>() == null)
                {
                    BoxCollider newCollider = newDoor.gameObject.AddComponent<BoxCollider>();
                    Debug.Log($"Added BoxCollider to {doorName}");
                }

                // Copy JrsDoorMechanic script
                JrsDoorMechanic templateMechanic = templateDoor.GetComponent<JrsDoorMechanic>();
                if (templateMechanic != null)
                {
                    JrsDoorMechanic newMechanic = newDoor.gameObject.AddComponent<JrsDoorMechanic>();
                    EditorUtility.CopySerialized(templateMechanic, newMechanic);
                    
                    // Assign door sounds
                    Debug.Log($"Searching for DoorOpenSound in {newVehicle.name}");
                    Transform doorOpenSound = FindChildRecursive(newVehicle.transform, "DoorOpenSound");
                    Debug.Log($"DoorOpenSound found: {doorOpenSound != null}");
                    Transform doorClosedSound = FindChildRecursive(newVehicle.transform, "DoorClosedSound");
                    
                    if (doorOpenSound != null)
                    {
                        AudioSource audioSource = doorOpenSound.GetComponent<AudioSource>();
                        if (audioSource == null)
                        {
                            audioSource = doorOpenSound.gameObject.AddComponent<AudioSource>();
                            newMechanic.openSound = audioSource;
                            Debug.Log($"Assigned DoorOpenSound to {doorName}");
                        }
                        else
                        {
                            // Check if the existing AudioSource has an AudioClip
                            if (audioSource.clip == null)
                            {
                                newMechanic.openSound = audioSource;
                                Debug.Log($"Assigned existing DoorOpenSound to {doorName}");
                            }
                            else
                            {
                                newMechanic.openSound = audioSource;
                                Debug.Log($"Assigned existing DoorOpenSound with AudioClip to {doorName}");
                            }
                        }
                    }
                    else
                    {
                        // Create new DoorOpenSound object if not found
                        Debug.Log($"Creating new DoorOpenSound for {doorName}");
                        GameObject doorSoundObj = new GameObject("DoorOpenSound");
                        doorSoundObj.transform.SetParent(newVehicle.transform);
                        AudioSource newAudio = doorSoundObj.AddComponent<AudioSource>();
                        newMechanic.openSound = newAudio;
                        Debug.Log($"Created new DoorOpenSound for {doorName}");
                    }

                    // Handle DoorClosedSound
                    if (doorClosedSound == null)
                    {
                        // Create new DoorClosedSound object if not found
                        doorClosedSound = new GameObject("DoorClosedSound").transform;
                        doorClosedSound.SetParent(newVehicle.transform);
                        doorClosedSound.localPosition = Vector3.zero;
                        AudioSource closedSource = doorClosedSound.gameObject.AddComponent<AudioSource>();
                        Debug.Log($"Created new DoorClosedSound object for {doorName}");
                    }
                    
                    // Ensure AudioSource exists
                    AudioSource closedAudio = doorClosedSound.GetComponent<AudioSource>();
                    if (closedAudio == null)
                    {
                        closedAudio = doorClosedSound.gameObject.AddComponent<AudioSource>();
                        Debug.Log($"Added AudioSource to DoorClosedSound for {doorName}");
                    }
                    
                    newMechanic.closeSound = closedAudio;
                    Debug.Log($"Assigned DoorClosedSound to {doorName}");
                    
                    Debug.Log($"Copied JrsDoorMechanic to {doorName}");
                }

                // Copy AudioSource
                AudioSource templateAudio = templateDoor.GetComponent<AudioSource>();
                if (templateAudio != null)
                {
                    AudioSource newAudio = newDoor.gameObject.AddComponent<AudioSource>();
                    EditorUtility.CopySerialized(templateAudio, newAudio);
                    Debug.Log($"Copied AudioSource to {doorName}");
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Error setting up doors: {ex.Message}");
        }
    }

   private void CopyAudioSources(GameObject source, GameObject destination)
    {
        AudioSource[] sources = source.GetComponentsInChildren<AudioSource>();
        foreach (AudioSource sourceAudio in sources)
        {
            Transform destTransform = destination.transform.Find(sourceAudio.transform.name);

            if (destTransform != null)
            {
                AudioSource destAudio = destTransform.GetComponent<AudioSource>();
                if (destAudio == null)
                {
                    destAudio = destTransform.gameObject.AddComponent<AudioSource>();
                }
                EditorUtility.CopySerialized(sourceAudio, destAudio);
            }
        }
    }

    private void CopyDustTrails(GameObject source, GameObject destination)
    {
        // Dust trails are now handled exclusively by AssignDustTrail method
        // This method is kept for backward compatibility but does nothing
    }

    private void CopyParticleSystems(GameObject source, GameObject destination)
    {
        if (source == null || destination == null)
        {
            Debug.LogWarning("Cannot copy particle systems - source or destination is null");
            return;
        }

        try
        {
            ParticleSystem[] systems = source.GetComponentsInChildren<ParticleSystem>();
            if (systems == null || systems.Length == 0)
            {
                Debug.LogWarning("No particle systems found in source object");
                return;
            }

            foreach (ParticleSystem sourceSystem in systems)
            {
                if (sourceSystem == null)
                {
                    Debug.LogWarning("Found null particle system in source");
                    continue;
                }

                // Skip dust trails since they're handled separately
                if (sourceSystem.transform.name.Contains("dust_trail")) continue;

                Transform destTransform = destination.transform.Find(sourceSystem.transform.name);
                if (destTransform == null)
                {
                    Debug.LogWarning($"Could not find transform {sourceSystem.transform.name} in destination");
                    continue;
                }

                try
                {
                    ParticleSystem destSystem = destTransform.gameObject.AddComponent<ParticleSystem>();
                    if (destSystem == null)
                    {
                        Debug.LogError($"Failed to add ParticleSystem to {destTransform.name}");
                        continue;
                    }

                    EditorUtility.CopySerialized(sourceSystem, destSystem);
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"Error copying particle system {sourceSystem.transform.name}: {ex.Message}");
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Error in CopyParticleSystems: {ex.Message}");
        }
    }

    private T CopyComponent<T>(GameObject source, GameObject destination) where T : Component
    {
        try
        {
            if (source == null || destination == null)
            {
                Debug.LogError("Cannot copy component - source or destination is null");
                return null;
            }

            T component = source.GetComponent<T>();
            if (component == null)
            {
                Debug.LogError($"Component of type {typeof(T)} not found on source object {source.name}");
                return null;
            }

            // Check if component already exists
            T existingComponent = destination.GetComponent<T>();
            if (existingComponent != null)
            {
                Debug.Log($"Component {typeof(T)} already exists on {destination.name} - updating values");
                EditorUtility.CopySerialized(component, existingComponent);
                return existingComponent;
            }

            // Add new component if it doesn't exist
            T newComponent = destination.AddComponent<T>();
            if (newComponent == null)
            {
                Debug.LogError($"Failed to add component of type {typeof(T)} to destination object {destination.name}");
                return null;
            }

            EditorUtility.CopySerialized(component, newComponent);
            Debug.Log($"Successfully copied {typeof(T)} from {source.name} to {destination.name}");
            return newComponent;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Error copying component: {ex.Message}\n{ex.StackTrace}");
            return null;
        }
    }

    private string GetTransformPath(Transform transform, Transform root)
    {
        if (transform == root) return "";
        if (transform.parent == null) return transform.name;
        return GetTransformPath(transform.parent, root) + "/" + transform.name;
    }

    private void CopyWheelColliderSettings(WheelCollider source, WheelCollider destination)
    {
        EditorUtility.CopySerialized(source, destination);
    }

    private Transform FindChildRecursive(Transform parent, string name)
    {
        // Case-insensitive search
        if (parent.name.Equals(name, System.StringComparison.OrdinalIgnoreCase))
        {
            return parent;
        }

        foreach (Transform child in parent)
        {
            Transform result = FindChildRecursive(child, name);
            if (result != null)
            {
                return result;
            }
        }

        return null;
    }

    private void LogChildObjects(Transform parent, string indent = "")
    {
        Debug.Log($"{indent}- {parent.name}");
        foreach (Transform child in parent)
        {
            LogChildObjects(child, indent + "  ");
        }
    }

    private void AssignDustTrail(JrsVehicleController controller, GameObject vehicle, string trailName, string fieldName)
    {
        // Find the template dust trail
        Transform templateTrail = FindChildRecursive(templateVehicle.transform, trailName);
        if (templateTrail == null)
        {
            Debug.LogError($"Could not find template dust trail: {trailName}");
            return;
        }

        // First try to find existing instance in hierarchy
        Transform trailTransform = FindChildRecursive(vehicle.transform, trailName);
        
        // If not found, check if it's a prefab instance
        if (trailTransform == null)
        {
            // Get all prefab instances in the scene
            var prefabInstances = Resources.FindObjectsOfTypeAll<GameObject>()
                .Where(go => go.name == trailName && PrefabUtility.IsPartOfPrefabInstance(go))
                .ToList();

            Debug.Log($"Found {prefabInstances.Count} prefab instances matching {trailName}");
            
            if (prefabInstances.Count > 0)
            {
                Debug.Log($"First prefab instance path: {AssetDatabase.GetAssetPath(prefabInstances[0])}");
                
                // Get the prefab source
                GameObject prefabSource = PrefabUtility.GetCorrespondingObjectFromSource(prefabInstances[0]);
                if (prefabSource != null)
                {
                    Debug.Log($"Found prefab source at: {AssetDatabase.GetAssetPath(prefabSource)}");
                    
                    // Instantiate new instance from prefab
                    GameObject newInstance = (GameObject)PrefabUtility.InstantiatePrefab(prefabSource);
                    newInstance.name = trailName;
                    newInstance.transform.SetParent(vehicle.transform);
                    
                    // Copy position/rotation from template
                    newInstance.transform.localPosition = templateTrail.localPosition;
                    newInstance.transform.localRotation = templateTrail.localRotation;
                    newInstance.transform.localScale = templateTrail.localScale;
                    
                    // Find the transform again
                    trailTransform = newInstance.transform;
                    Debug.Log($"Instantiated dust trail prefab: {trailName} at {trailTransform.position}");
                }
                else
                {
                    Debug.LogError($"Could not get prefab source for {trailName}");
                }
            }
            else
            {
                Debug.LogError($"No prefab instances found for {trailName}");
            }
        }
        else
        {
            // If found existing instance, update its position/rotation
            trailTransform.localPosition = templateTrail.localPosition;
            trailTransform.localRotation = templateTrail.localRotation;
            trailTransform.localScale = templateTrail.localScale;
        }

        if (trailTransform == null)
        {
            Debug.LogError($"Could not find dust trail object: {trailName}");
            return;
        }

        ParticleSystem particleSystem = trailTransform.GetComponent<ParticleSystem>();
        if (particleSystem == null)
        {
            Debug.LogError($"Dust trail object {trailName} has no ParticleSystem component");
            return;
        }

        // Assign to appropriate field using reflection
        System.Reflection.FieldInfo field = typeof(JrsVehicleController).GetField(fieldName);
        if (field != null)
        {
            // Verify field type is ParticleSystem
            if (field.FieldType == typeof(ParticleSystem))
            {
                field.SetValue(controller, particleSystem);
                Debug.Log($"Successfully assigned {trailName} to {fieldName}");
                
                // Verify assignment
                ParticleSystem assignedSystem = (ParticleSystem)field.GetValue(controller);
                if (assignedSystem == particleSystem)
                {
                    Debug.Log($"Assignment verification successful for {fieldName}");
                }
                else
                {
                    Debug.LogError($"Assignment verification failed for {fieldName}");
                }
            }
            else
            {
                Debug.LogError($"Field {fieldName} is not of type ParticleSystem");
            }
        }
        else
        {
            Debug.LogError($"Could not find field {fieldName} in JrsVehicleController");
        }
    }

    private void AssignLightElements(GameObject vehicle)
    {
        JrsVehicleLightControl lightControl = vehicle.GetComponent<JrsVehicleLightControl>();
        if (lightControl == null)
        {
            Debug.LogError("JrsVehicleLightControl component not found");
            return;
        }

        // Initialize all light element arrays with proper sizes
        lightControl.headlightElements = new GameObject[2]; // Headlights + Headlight_source
        lightControl.signalElements = new GameObject[0];
        lightControl.extraLightsElements = new GameObject[2];
        lightControl.reverseLightsElements = new GameObject[0];
        lightControl.brakeLightsElements = new GameObject[0];

        // Array of light object names to find
        string[] lightNames = new string[] {
            "headlight",
            "headlight_source",
            "turn_signal",
            "roof_light",
            "fog_light", 
            "reverse_light",
            "brake_light"
        };

            // Assign headlights
            Transform headlights = FindChildRecursive(vehicle.transform, "headlight");
            Transform headlightSource = FindChildRecursive(vehicle.transform, "headlight_source");
            
            if (headlights != null)
            {
                // Assign main Headlights object to index 0
                lightControl.headlightElements[0] = headlights.gameObject;
                headlights.gameObject.SetActive(false); // Disable headlight
                
                // Assign Headlight_source to index 1 if found
                if (headlightSource != null)
                {
                    lightControl.headlightElements[1] = headlightSource.gameObject;
                    headlightSource.gameObject.SetActive(false); // Disable headlight source
                }
                
                AddLog($"Assigned {lightControl.headlightElements.Length} headlight elements", Color.green);
            }
            else
            {
                AddLog("Could not find headlight transform", Color.yellow);
            }

            // Assign turn signals
            Transform turnSignals = FindChildRecursive(vehicle.transform, "turn_signal");
            if (turnSignals != null)
            {
                lightControl.signalElements = GetChildGameObjects(turnSignals);
                foreach (var signal in lightControl.signalElements)
                {
                    signal.SetActive(false); // Disable all turn signals
                }
                AddLog($"Assigned {lightControl.signalElements.Length} signal elements", Color.green);
            }
            else
            {
                AddLog("Could not find turn_signal transform", Color.yellow);
            }

            // Assign roof lights
            Transform roofLights = FindChildRecursive(vehicle.transform, "roof_light");
            Transform foglight = FindChildRecursive(vehicle.transform, "fog_light");

            if (roofLights != null)
            {
                // Assign roof_light object to index 0
                lightControl.extraLightsElements[0] = roofLights.gameObject;
                roofLights.gameObject.SetActive(false); // Disable roof light

                // Assign fog_light to index 1 if found
                if (foglight != null)
                {
                    lightControl.extraLightsElements[1] = foglight.gameObject;
                    foglight.gameObject.SetActive(false); // Disable fog light
                }

                AddLog($"Assigned {lightControl.extraLightsElements.Length} extra light elements", Color.green);
            }
            else
            {
                AddLog("Could not find extra lights transform", Color.yellow);
            }

            // Assign reverse lights
            Transform reverseLights = FindChildRecursive(vehicle.transform, "reverse_light");
            if (reverseLights != null)
            {
                lightControl.reverseLightsElements = GetChildGameObjects(reverseLights);
                foreach (var light in lightControl.reverseLightsElements)
                {
                    if (light.name.ToLower() != "reverse" && !light.name.ToLower().Contains("reverse_light")) light.SetActive(false); // Disable all reverse lights
                }
                AddLog($"Assigned {lightControl.reverseLightsElements.Length} reverse light elements", Color.green);
            }
            else
            {
                AddLog("Could not find reverse_light transform", Color.yellow);
            }

            // Assign brake lights
            Transform brakeLights = FindChildRecursive(vehicle.transform, "brake_light");
            if (brakeLights != null)
            {
                lightControl.brakeLightsElements = GetChildGameObjects(brakeLights);
                foreach (var light in lightControl.brakeLightsElements)
                {
                    light.SetActive(false); // Disable all brake lights
                }
                AddLog($"Assigned {lightControl.brakeLightsElements.Length} brake light elements", Color.green);
            }
            else
            {
                AddLog("Could not find brake_light transform", Color.yellow);
            }

        // Force serialization
        EditorUtility.SetDirty(lightControl);
        PrefabUtility.RecordPrefabInstancePropertyModifications(lightControl);
        AddLog("All light elements assigned and serialized", Color.green);
    }

    private GameObject[] GetChildGameObjects(Transform parent)
    {
        List<GameObject> children = new List<GameObject>();
        
        AddLog($"Searching for light elements under {parent.name}", Color.white);
        
        // Check if parent itself has light components
        if (HasLightComponent(parent))
        {
            AddLog($"Found light component on parent: {parent.name}", Color.green);
            children.Add(parent.gameObject);
        }

        // Get direct children
        foreach (Transform child in parent)
        {
            AddLog($"Checking child: {child.name}", Color.white);
            
            // Add the child itself if it has any light-related components
            if (HasLightComponent(child))
            {
                AddLog($"Adding light element: {child.name}", Color.green);
                children.Add(child.gameObject);
            }
            
            // Recursively add any nested light objects
            var nestedChildren = GetChildGameObjects(child);
            if (nestedChildren.Length > 0)
            {
                AddLog($"Found {nestedChildren.Length} nested light elements under {child.name}", Color.white);
                children.AddRange(nestedChildren);
            }
        }
        
        AddLog($"Found total {children.Count} light elements under {parent.name}", Color.white);
        return children.ToArray();
    }

    private bool HasLightComponent(Transform transform)
    {
        // Check for various light-related components
        if (transform.GetComponent<Light>() != null)
        {
            AddLog($"Found Light component on {transform.name}", Color.green);
            return true;
        }
        
        if (transform.GetComponent<MeshRenderer>() != null)
        {
            AddLog($"Found MeshRenderer component on {transform.name}", Color.green);
            return true;
        }
        
        if (transform.GetComponent<LightProbeProxyVolume>() != null)
        {
            AddLog($"Found LightProbeProxyVolume component on {transform.name}", Color.green);
            return true;
        }
        
        if (transform.GetComponent<ReflectionProbe>() != null)
        {
            AddLog($"Found ReflectionProbe component on {transform.name}", Color.green);
            return true;
        }
        
        if (transform.GetComponent<LensFlare>() != null)
        {
            AddLog($"Found LensFlare component on {transform.name}", Color.green);
            return true;
        }
        
        if (transform.GetComponent<Projector>() != null)
        {
            AddLog($"Found Projector component on {transform.name}", Color.green);
            return true;
        }
        
        return false;
    }


    private void SetupSteering(GameObject template, GameObject newVehicle)
    {
        try
        {
            // Find template steering parent
            Transform templateSteering = FindChildRecursive(template.transform, "steering");
            if (templateSteering == null)
            {
                AddLog("Could not find steering in template vehicle", Color.yellow);
                return;
            }

            // Create new steering parent object
            GameObject steeringParent = new GameObject("steering");
            steeringParent.transform.SetParent(newVehicle.transform);
            
            // Set steering parent position/rotation to match template
            steeringParent.transform.position = templateSteering.position;
            steeringParent.transform.rotation = templateSteering.rotation;

            // Find or create new steering wheel
            Transform newSteeringWheel = FindChildRecursive(newVehicle.transform, "steering_wheel");

            // Copy steering animator component from template steering to new steering
            JrsSteeringWheelAnimator templateAnimator = templateSteering.GetComponent<JrsSteeringWheelAnimator>();
            if (templateAnimator != null)
            {
                JrsSteeringWheelAnimator newAnimator = steeringParent.AddComponent<JrsSteeringWheelAnimator>();
                EditorUtility.CopySerialized(templateAnimator, newAnimator);
                
                // Assign steering wheel transform to animator
                newAnimator.steeringWheelTransform = newSteeringWheel;
                AddLog("Copied JrsSteeringWheelAnimator and assigned steering wheel transform", Color.green);
            }
            if (newSteeringWheel == null)
            {
                AddLog("Could not find steering_wheel in new vehicle - creating new one", Color.yellow);
                newSteeringWheel = new GameObject("steering_wheel").transform;
                
                // Parent steering wheel to steering parent
                newSteeringWheel.SetParent(steeringParent.transform);
                
                // Reset steering wheel local transform
                newSteeringWheel.localPosition = Vector3.zero;
                newSteeringWheel.localRotation = Quaternion.identity;
                newSteeringWheel.localScale = Vector3.one;
            }
            else
            {
                // Parent existing steering wheel to steering parent
                newSteeringWheel.SetParent(steeringParent.transform);
            }

            AddLog("Steering setup complete", Color.green);
        }
        catch (System.Exception ex)
        {
            AddLog($"Error setting up steering: {ex.Message}", Color.red);
        }
    }

    private void CopyGameObjects(GameObject source, GameObject destination)
    {
        try
        {
            // Array of object names to copy
            string[] objectNames = new string[] {
                "player",
                "engine_sound",
                "start_sound",
                "target",
                "headlight_source"
            };

            AddLog("Template vehicle hierarchy:", Color.white);
            LogChildObjects(source.transform);
            
            foreach (string objName in objectNames)
            {
                // Find object in template recursively
                Transform templateObj = FindChildRecursive(source.transform, objName);
                if (templateObj == null)
                {
                    AddLog($"Object {objName} not found in template vehicle. **Please rename this object in the template.** Available objects:", Color.yellow);
                    LogChildObjects(source.transform);
                    continue;
                }

                // Create new object in destination
                GameObject newObj = Instantiate(templateObj.gameObject);
                newObj.name = objName;
                newObj.transform.SetParent(destination.transform);
                newObj.transform.localPosition = templateObj.localPosition;
                newObj.transform.localRotation = templateObj.localRotation;
                newObj.transform.localScale = templateObj.localScale;

                // Copy all components with detailed logging
                Component[] components = templateObj.GetComponents<Component>();
                AddLog($"Copying components from {templateObj.name} ({components.Length} components)", Color.white);
                
                foreach (Component component in components)
                {
                    if (component == null || component is Transform) continue;
                    
                    try 
                    {
                        // Check if component already exists
                        Component existingComponent = newObj.GetComponent(component.GetType());
                        if (existingComponent != null)
                        {
                            AddLog($"Component {component.GetType()} already exists on {newObj.name} - skipping add", Color.white);
                            EditorUtility.CopySerialized(component, existingComponent);
                        }
                        else
                        {
                            AddLog($"Adding new component {component.GetType()} to {newObj.name}", Color.white);
                            Component newComponent = newObj.AddComponent(component.GetType());
                            EditorUtility.CopySerialized(component, newComponent);
                        }
                    }
                    catch (System.Exception ex)
                    {
                        AddLog($"Failed to copy component {component.GetType()} to {newObj.name}: {ex.Message}", Color.red);
                    }
                }

                // Special handling for audio sources
                var vehicleController = destination.GetComponent<JrsVehicleController>();
                if (vehicleController != null)
                {
                    if (objName == "engine_sound" || objName == "start_sound")
                    {
                        AudioSource audioSource = newObj.GetComponent<AudioSource>();
                        if (audioSource != null)
                        {
                            if (objName == "engine_sound")
                            {
                                vehicleController.engineAudioSource = audioSource;
                            }
                            else if (objName == "start_sound")
                            {
                                vehicleController.engineStartAudioSource = audioSource;
                            }
                            
                            // Force serialization
                            EditorUtility.SetDirty(vehicleController);
                            PrefabUtility.RecordPrefabInstancePropertyModifications(vehicleController);
                            AddLog($"Assigned {objName} audio source to controller", Color.green);
                        }
                    }
                }
            }
        }
        catch (System.Exception ex)
        {
            AddLog($"Error copying game objects: {ex.Message}", Color.red);
        }
    }
}
