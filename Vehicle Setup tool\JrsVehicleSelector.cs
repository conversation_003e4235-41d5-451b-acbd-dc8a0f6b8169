using UnityEngine;
using System.Collections.Generic;

public class JrsVehicleSelector : MonoBehaviour
{
    public List<JrsVehicleController> vehicles;
    public JrsCameraManager cameraManager;
    private int currentVehicleIndex = 0;
    private JrsVehicleController currentVehicle;
    private JrsVehicleLightControl currentLightControl;

    public JrsVehicleController GetCurrentVehicle()
    {
        return currentVehicle;
    }

    public JrsVehicleLightControl GetCurrentLightControl()
    {
        return currentLightControl;
    }

    void Start()
    {
        // Disable JrsVehicleController on all vehicles
        foreach (JrsVehicleController vehicle in vehicles)
        {
            if (vehicle != null)
            {
                vehicle.GetComponent<JrsVehicleController>().canControl = false;
            }
        }

        // Set the camera to the first vehicle
        if (cameraManager != null && vehicles.Count > 0)
        {
            SwitchVehicle(0);
        }
    }

    void Update()
    {
        // Check for Page Up/Page Down key presses to switch vehicles
        if (Input.GetKeyDown(KeyCode.PageUp))
        {
            SwitchVehicle(currentVehicleIndex + 1);
        }
        else if (Input.GetKeyDown(KeyCode.PageDown))
        {
            SwitchVehicle(currentVehicleIndex - 1);
        }
        
        // Check for left mouse click to select vehicle
        if (Input.GetMouseButtonDown(0))
        {
            SelectVehicleWithMouse();
        }
    }

    private void SelectVehicleWithMouse()
    {
        if (Camera.main == null)
        {
            Debug.LogWarning("Main camera not found");
            return;
        }

        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        if (!Physics.Raycast(ray, out RaycastHit hit))
        {
            Debug.Log("No vehicle clicked");
            return;
        }

        if (hit.collider == null)
        {
            Debug.LogWarning("Clicked object has no collider");
            return;
        }

        JrsVehicleController clickedVehicle = hit.collider.GetComponent<JrsVehicleController>();
        if (clickedVehicle == null)
        {
            Debug.Log("Clicked object is not a vehicle");
            return;
        }

        if (!vehicles.Contains(clickedVehicle))
        {
            Debug.LogWarning("Clicked vehicle is not in vehicles list");
            return;
        }

        int index = vehicles.IndexOf(clickedVehicle);
        SwitchVehicle(index);
    }

    public void SwitchVehicle(int index)
    {
        // Remove null entries from the list before switching
        vehicles.RemoveAll(item => item == null);

        // Wrap around the index
        if (index < 0)
        {
            index = vehicles.Count - 1;
        }
        else if (index >= vehicles.Count)
        {
            index = 0;
        }

        if (vehicles.Count == 0) return;

        // Disable vehicle controller on the previously selected vehicle
        if (currentVehicle != null)
        {
            currentVehicle.SetActive(false);
            currentVehicle.GetComponent<JrsVehicleController>().canControl = false;
        }

        // Enable vehicle controller on the newly selected vehicle
        currentVehicleIndex = index;
        currentVehicle = vehicles[currentVehicleIndex];
        currentVehicle.SetActive(true);
        currentVehicle.GetComponent<JrsVehicleController>().canControl = true;

        // Update light control reference
        currentLightControl = currentVehicle.GetComponent<JrsVehicleLightControl>();
        if (currentLightControl == null)
        {
            Debug.LogWarning($"No JrsVehicleLightControl component found on vehicle: {currentVehicle.name}");
        }

        // Switch camera
        UpdateCameratargets(currentVehicle.transform);
    }

    private void UpdateCameratargets(Transform vehicleTransform)
    {
        //Find the target object within the vehicle
        Transform target = vehicleTransform.Find("target");

        if (target == null)
        {
            Debug.LogError("target object not found in vehicle: " + vehicleTransform.name);
            return;
        }

       cameraManager.SetActiveCamera(vehicleTransform);
    }
}
