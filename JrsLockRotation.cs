using UnityEngine;

public class JrsLockRotation : MonoBehaviour
{
    public Transform wheel_mesh; // Reference to the wheel_mesh object

    private Vector3 initialPosition; // Initial position of the object

    private void Start()
    {
        initialPosition = transform.localPosition;
    }

    private void Update()
    {
        // Calculate the position offset based on the wheel_mesh's forward direction
        Vector3 positionOffset = wheel_mesh.forward * initialPosition.z;

        // Sync the position with the wheel_mesh
        transform.position = wheel_mesh.position + positionOffset;
    }
}
