using UnityEngine;

public class JrsDashCamera : MonoBehaviour
{
    public Transform player;
    public Vector3 cameraOffset = new Vector3(0f, 1.5f, -5f);
    public float cameraSensitivity = 2f;

    private Vector3 targetPosition;
    private Quaternion targetRotation;

    private void LateUpdate()
    {
        targetPosition = player.position + player.TransformDirection(cameraOffset);
        targetRotation = Quaternion.Euler(player.eulerAngles.x, player.eulerAngles.y, 0f);

        transform.position = Vector3.Lerp(transform.position, targetPosition, cameraSensitivity * Time.deltaTime);
        transform.rotation = Quaternion.Lerp(transform.rotation, targetRotation, cameraSensitivity * Time.deltaTime);
    }
}
